#!/usr/bin/env python3
"""
Test script for enhanced AssemblyAI Hindi transcription support
Tests the updated configuration, language detection, and transcription flow
"""

import os
import sys
import time
import asyncio
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tutor.modules.audio.transcription_factory import TranscriptionFactory, TranscriptionServiceError
from tutor.modules.models.classes import env
from tutor.executors.entry_dumper import EntryDumper
from tutor.executors.request_handler import RequestHandler
import logging


class MockUser:
    """Mock user object for testing"""
    def __init__(self):
        self.mobile = "1234567890"
        self.session = "test_session_" + str(int(time.time()))
        self.detected_language = None
        self.stream_id = "mock_stream_456"
        self.loop = asyncio.get_event_loop()
        self.ai_start_listening = True
        
        # Add bot manager mock
        self.bot_manager = MockBotManager()
    
    def sync_send(self, data):
        print(f"[MockUser] Sending: {data}")
    
    def send_end_call(self):
        print("[MockUser] End call signal sent")
    
    async def send_media_event(self, audio_data, mark_name, is_protocol_format=False):
        print(f"[MockUser] Media event sent: mark={mark_name}, audio_size={len(audio_data)}, protocol={is_protocol_format}")


class MockBotManager:
    """Mock bot manager for testing"""
    def start_new_call(self):
        return "नमस्ते! आईसीआईसीआई बैंक में आपका स्वागत है। Hello! Welcome to ICICI Bank! How can I help you today?"
    
    async def handle_user_input(self, user_input):
        # Simulate AI response in the same language as input
        if any(0x0900 <= ord(char) <= 0x097F for char in user_input):  # Contains Devanagari
            return f"आपने कहा: {user_input}. मैं आपकी मदद कर सकता हूं।"
        else:
            return f"You said: {user_input}. I can help you with that."


class MockLogger:
    """Mock logger for testing"""
    def info(self, msg, *args):
        print(f"[INFO] {msg % args if args else msg}")
    
    def error(self, msg, *args):
        print(f"[ERROR] {msg % args if args else msg}")
    
    def warning(self, msg, *args):
        print(f"[WARNING] {msg % args if args else msg}")
    
    def debug(self, msg, *args):
        print(f"[DEBUG] {msg % args if args else msg}")


def test_transcription_factory_config():
    """Test if the transcription factory is properly configured for AssemblyAI Hindi"""
    print("=== Testing Transcription Factory Configuration ===")
    
    # Check current configuration
    print(f"Default transcription service: {env.transcription_service}")
    print(f"AssemblyAI API key configured: {'Yes' if env.assemblyai_api_key else 'No'}")
    print(f"AssemblyAI language code: {env.assemblyai_language_code}")
    print(f"AssemblyAI language options: {env.assemblyai_language_options}")
    print(f"AssemblyAI auto-detection enabled: {env.assemblyai_enable_auto_detection}")
    
    if env.transcription_service == "assemblyai":
        print("✓ AssemblyAI is configured as primary transcription service")
        return True
    else:
        print(f"✗ AssemblyAI is not primary service (current: {env.transcription_service})")
        return False


def test_assemblyai_service_creation():
    """Test creating AssemblyAI service with Hindi configuration"""
    print("\n=== Testing AssemblyAI Service Creation ===")
    
    try:
        # Create mock user and logger
        user = MockUser()
        logger = MockLogger()
        
        # Test service creation
        transcription_service = TranscriptionFactory.create_transcription_service(
            user=user,
            logger_instance=logger,
            service_type="assemblyai"
        )
        
        print("✓ AssemblyAI transcription service created successfully")
        
        # Check if Hindi configuration is applied
        if hasattr(transcription_service, 'language_code'):
            print(f"✓ Language code configured: {transcription_service.language_code}")
        
        if hasattr(transcription_service, 'language_options'):
            print(f"✓ Language options configured: {transcription_service.language_options}")
            
        if hasattr(transcription_service, 'enable_auto_detection'):
            print(f"✓ Auto-detection enabled: {transcription_service.enable_auto_detection}")
        
        return transcription_service
        
    except TranscriptionServiceError as e:
        print(f"✗ Failed to create AssemblyAI service: {e}")
        return None
    except Exception as e:
        print(f"✗ Unexpected error creating AssemblyAI service: {e}")
        return None


def test_language_detection():
    """Test the language detection functionality"""
    print("\n=== Testing Language Detection ===")
    
    # Create a mock transcription service to test language detection
    from tutor.modules.audio.assembly_ai.realtime_transcription import RealtimeTranscription
    
    try:
        user = MockUser()
        logger = MockLogger()
        
        transcription = RealtimeTranscription(
            api_key=env.assemblyai_api_key,
            user=user,
            logger=logger,
            language_code="auto",
            language_options=["hi", "en"],
            enable_auto_detection=True
        )
        
        # Test language detection with different texts
        test_cases = [
            ("Hello, how are you?", "en"),
            ("नमस्ते, आप कैसे हैं?", "hi"),
            ("My name is John", "en"),
            ("मेरा नाम राम है।", "hi"),
            ("Hello नमस्ते mixed text", "en"),  # Mixed should default to en
            ("", "en"),  # Empty should default to en
        ]
        
        for text, expected_lang in test_cases:
            detected_lang = transcription._detect_language_from_text(text)
            status = "✓" if detected_lang == expected_lang else "✗"
            print(f"{status} '{text}' -> {detected_lang} (expected: {expected_lang})")
        
        return True
        
    except Exception as e:
        print(f"✗ Language detection test failed: {e}")
        return False


async def test_tts_integration():
    """Test TTS integration with detected language"""
    print("\n=== Testing TTS Integration ===")
    
    try:
        # Create mock objects
        user = MockUser()
        logger = MockLogger()
        
        # Create entry dumper and request handler
        entry_dumper = EntryDumper(user.mobile, user.session, "test_file.yaml")
        request_handler = RequestHandler(user, entry_dumper, logger)
        
        # Test cases with different languages
        test_cases = [
            ("Hello! How can I help you today?", "en"),
            ("नमस्ते! मैं आपकी कैसे मदद कर सकता हूं?", "hi"),
        ]
        
        for text, expected_lang in test_cases:
            print(f"Testing TTS for: '{text}'")
            
            # Set detected language in user object
            user.detected_language = expected_lang
            
            # Test language validation
            validated_lang = request_handler.validate_and_update_language(text)
            print(f"✓ Language validated as: {validated_lang}")
            
            # Test TTS audio generation (mock)
            print(f"✓ Would generate TTS audio in language: {validated_lang}")
        
        return True
        
    except Exception as e:
        print(f"✗ TTS integration test failed: {e}")
        return False


def test_service_health():
    """Test service health checks"""
    print("\n=== Testing Service Health ===")
    
    # Check available services
    available_services = TranscriptionFactory.get_available_services()
    print(f"Available transcription services: {available_services}")
    
    # Check AssemblyAI health
    is_healthy, status_message = TranscriptionFactory.check_service_health("assemblyai")
    if is_healthy:
        print(f"✓ AssemblyAI service health: {status_message}")
    else:
        print(f"✗ AssemblyAI service health: {status_message}")
    
    return is_healthy


async def main():
    """Main test function"""
    print("Enhanced AssemblyAI Hindi Transcription Test Suite")
    print("=" * 55)
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Configuration check
    if test_transcription_factory_config():
        tests_passed += 1
    
    # Test 2: Service creation
    if test_assemblyai_service_creation():
        tests_passed += 1
    
    # Test 3: Language detection
    if test_language_detection():
        tests_passed += 1
    
    # Test 4: TTS integration
    if await test_tts_integration():
        tests_passed += 1
    
    # Test 5: Service health
    if test_service_health():
        tests_passed += 1
    
    # Summary
    print(f"\n=== Test Results ===")
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Enhanced AssemblyAI Hindi support is working correctly.")
        print("\n📋 Next Steps:")
        print("1. Test with real Hindi audio input")
        print("2. Monitor accuracy improvements over AWS Transcribe")
        print("3. Verify fallback mechanisms work properly")
    else:
        print("⚠️  Some tests failed. Please check the configuration and errors above.")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
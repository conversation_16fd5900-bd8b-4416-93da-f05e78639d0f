import asyncio
import io
import json
import threading
import time
import uuid
from collections import deque
from typing import Optional, Dict, Any

import boto3
from amazon_transcribe.client import TranscribeStreaming<PERSON>lient

from tutor.executors.entry_dumper import EntryDumper
from tutor.executors.request_handler import RequestHandler


class AWSTranscribeError(Exception):
    """Custom exception for AWS Transcribe errors"""
    pass


# TranscriptHandler class removed - using direct stream output processing instead


class RealtimeTranscription:
    """AWS Transcribe real-time transcription service"""
    
    def __init__(self, region, access_key_id, secret_access_key, user, logger, sample_rate=16000, 
                 language_code="auto", language_options=None, media_encoding="pcm", vocabulary_name=None, 
                 vocabulary_filter_name=None, enable_partial_results_stabilization=True,
                 partial_results_stability="medium"):
        self.user = user
        self.logger = logger
        self.session = user.session
        self.file_name = "realtime_transcription.yaml"
        
        # AWS Transcribe configuration
        self.region = region
        self.access_key_id = access_key_id
        self.secret_access_key = secret_access_key
        self.language_code = language_code
        self.language_options = language_options or ["hi-IN", "en-US"]
        self.sample_rate = sample_rate
        self.media_encoding = media_encoding
        self.vocabulary_name = vocabulary_name
        self.vocabulary_filter_name = vocabulary_filter_name
        self.enable_partial_results_stabilization = enable_partial_results_stabilization
        self.partial_results_stability = partial_results_stability
        
        # Language detection and session management
        self.detected_language = None
        self.detected_language_code = None
        
        # Audio processing
        self.audio_queue = deque()
        self.listen_task_threading = threading.Thread(target=self.stream_audio_data, daemon=True)
        self.is_stream_audio_data = True
        self.realtime_final_transcript = None
        self.last_transcript_time = None
        
        # Thread synchronization
        self.transcript_lock = threading.Lock()
        
        # Transcription state
        self.is_completed = True
        self.user_input = ""
        self.last_speak_time = time.time()
        self.last_llm_answer = None
        self.conversation_history = []
        
        # AWS clients and stream
        self.transcribe_client: Optional[TranscribeStreamingClient] = None
        self.stream = None
        self.session_id = str(uuid.uuid4())
        
        # Integration components
        self.entry_dumper = EntryDumper(self.user.mobile, self.session, self.file_name)
        self.request_handler = RequestHandler(self.user, self.entry_dumper, logger)
        
        # Initialize AWS Transcribe client
        self._initialize_client()

    def _initialize_client(self):
        """Initialize AWS Transcribe streaming client"""
        try:
            # Create boto3 session with credentials
            import os
            
            # Set environment variables for AWS credentials if not already set
            if self.access_key_id and self.secret_access_key:
                os.environ['AWS_ACCESS_KEY_ID'] = self.access_key_id
                os.environ['AWS_SECRET_ACCESS_KEY'] = self.secret_access_key
            if self.region:
                os.environ['AWS_DEFAULT_REGION'] = self.region
            
            # Initialize the TranscribeStreamingClient (credentials handled via environment)
            self.transcribe_client = TranscribeStreamingClient(
                region=self.region
            )
            self.logger.info("AWS Transcribe client initialized successfully")
        except Exception as e:
            error_msg = f"Failed to initialize AWS Transcribe client: {e}"
            self.logger.error(error_msg)
            raise AWSTranscribeError(error_msg)

    def on_open(self):
        """Called when transcription session opens (compatibility method)"""
        self.logger.info("AWS Transcribe Session ID: %s", self.session_id)

    def on_data(self, transcript_text: str, is_final: bool = False):
        """Handle transcription data (compatibility method)"""
        if not transcript_text:
            return
        
        current_time = time.time()
        if is_final:
            self.logger.info("FinalTranscript: %s", transcript_text)
            self.user_input += transcript_text
        else:
            self.request_handler.handle_partial_transcript(user_input=transcript_text)
        
        self.last_transcript_time = current_time
        self.last_speak_time = time.time()

    def on_error(self, error):
        """Handle transcription errors (compatibility method)"""
        self.logger.error("AWS Transcribe error: %s", error)
        
        error_message = str(error)
        if "session" in error_message.lower() or "timeout" in error_message.lower():
            self.logger.info("Session error detected, triggering cleanup")
            try:
                self.is_stream_audio_data = False
                if hasattr(self.user, 'end_ai_call'):
                    self.user.end_ai_call()
            except Exception as cleanup_error:
                self.logger.error("Error during cleanup: %s", cleanup_error)

    def on_close(self):
        """Called when transcription session closes (compatibility method)"""
        self.logger.info("AWS Transcribe Session Closed")

    def on_extra_session_information(self, data: Dict[str, Any]):
        """Handle extra session information (compatibility method)"""
        duration = data.get('audio_duration_seconds', 0)
        self.entry_dumper.start_dump_task("audio_duration_seconds", f"{duration} seconds")
        self.logger.info("Session information: %s seconds", duration)

    async def _start_streaming(self):
        """Start AWS Transcribe streaming"""
        try:
            # Create stream parameters
            stream_params = {
                'media_sample_rate_hz': self.sample_rate,
                'media_encoding': self.media_encoding
            }
            
            # Configure language detection
            if self.language_code == "auto":
                # Use English as default for streaming, detect language client-side
                # Note: AWS Transcribe streaming API has limited identify_language support
                stream_params['language_code'] = 'en-US'
                self.logger.info("AWS Transcribe configured for auto-detection fallback with default language: en-US")
                self.logger.info("Will perform client-side language detection from transcripts")
            else:
                # Use specific language code
                stream_params['language_code'] = self.language_code
                self.logger.info("AWS Transcribe configured for language: %s", self.language_code)
            
            # Add optional parameters
            if self.vocabulary_name:
                stream_params['vocabulary_name'] = self.vocabulary_name
            if self.vocabulary_filter_name:
                stream_params['vocabulary_filter_name'] = self.vocabulary_filter_name
            if self.enable_partial_results_stabilization:
                stream_params['enable_partial_results_stabilization'] = self.enable_partial_results_stabilization
                stream_params['partial_results_stability'] = self.partial_results_stability
            
            # Start transcription stream and set up output handling
            if self.transcribe_client:
                self.stream = await self.transcribe_client.start_stream_transcription(
                    **stream_params
                )
                # Set up a task to handle transcript output
                import asyncio
                asyncio.create_task(self._handle_transcript_output())
            
            self.on_open()
            self.logger.info("AWS Transcribe streaming started")
            
        except Exception as e:
            error_msg = f"Failed to start AWS Transcribe streaming: {e}"
            self.logger.error(error_msg)
            self.on_error(error_msg)
            raise AWSTranscribeError(error_msg)

    async def _handle_transcript_output(self):
        """Handle transcript output from AWS Transcribe stream"""
        try:
            if not self.stream or not hasattr(self.stream, 'output_stream'):
                self.logger.error("No output stream available for transcript handling")
                return

            self.logger.info("Starting AWS Transcribe output handler")
            
            # Listen for transcript events from the output stream
            async for event in self.stream.output_stream:
                if hasattr(event, 'transcript'):
                    results = event.transcript.results
                    for result in results:
                        # Note: AWS language identification removed since we're using client-side detection
                        # when language_code="auto" due to streaming API limitations
                        
                        if result.alternatives:
                            transcript = result.alternatives[0].transcript
                            if transcript:
                                current_time = time.time()
                                
                                # Perform client-side language detection if auto-detection is enabled
                                if self.language_code == "auto":
                                    detected_lang = self._detect_language_from_text(transcript)
                                    if detected_lang != self.detected_language:
                                        self.detected_language = detected_lang
                                        self.detected_language_code = f"{detected_lang}-{'IN' if detected_lang == 'hi' else 'US'}"
                                        self.logger.info("Client-side detected language: %s (%s)", 
                                                        self.detected_language, self.detected_language_code)
                                        # Store language in user session
                                        if hasattr(self.user, 'detected_language'):
                                            self.user.detected_language = self.detected_language
                                        # Store in entry dumper for session tracking
                                        self.entry_dumper.start_dump_task("detected_language", 
                                                                         f"{self.detected_language} (client-side detection)")
                                
                                if result.is_partial == False:
                                    # Final transcript - use lock for thread safety
                                    lang_info = f"[{self.detected_language or 'unknown'}]" if self.detected_language else ""
                                    self.logger.info("AWS FinalTranscript %s: %s", lang_info, transcript)
                                    with self.transcript_lock:
                                        self.user_input += transcript
                                        self.last_transcript_time = current_time
                                        self.last_speak_time = time.time()
                                else:
                                    # Partial transcript
                                    lang_info = f"[{self.detected_language or 'unknown'}]" if self.detected_language else ""
                                    self.logger.info("AWS PartialTranscript %s: %s", lang_info, transcript)
                                    self.request_handler.handle_partial_transcript(user_input=transcript)
                                    # Update last_speak_time with lock for consistency
                                    with self.transcript_lock:
                                        self.last_speak_time = time.time()
                                
        except Exception as e:
            self.logger.error("Error in transcript output handler: %s", e)

    def _map_language_code(self, aws_language_code: str) -> str:
        """Map AWS Transcribe language codes to simple language identifiers"""
        language_map = {
            'hi-IN': 'hi',    # Hindi
            'en-US': 'en',    # English
            'en-GB': 'en',    # English (British)
            'es-ES': 'es',    # Spanish
            'fr-FR': 'fr',    # French
            'de-DE': 'de',    # German
            'ar-AE': 'ar',    # Arabic
            'zh-CN': 'zh',    # Chinese
        }
        return language_map.get(aws_language_code, 'en')  # Default to English
    
    def _detect_language_from_text(self, text: str) -> str:
        """Detect language from text using character analysis
        
        Args:
            text: The text to analyze for language detection
            
        Returns:
            Language code ('hi' for Hindi, 'en' for English)
        """
        if not text or not text.strip():
            return 'en'  # Default to English for empty text
        
        # Count Devanagari characters (Hindi script)
        devanagari_chars = 0
        latin_chars = 0
        total_chars = 0
        
        for char in text:
            if char.isalpha():
                total_chars += 1
                # Check if character is in Devanagari range (Hindi)
                if 0x0900 <= ord(char) <= 0x097F:
                    devanagari_chars += 1
                # Check if character is in Latin range (English)
                elif 0x0041 <= ord(char) <= 0x007A:
                    latin_chars += 1
        
        if total_chars == 0:
            return 'en'  # Default to English if no alphabetic characters
        
        # Calculate percentages
        devanagari_percentage = (devanagari_chars / total_chars) * 100
        
        # If more than 30% of characters are Devanagari, consider it Hindi
        if devanagari_percentage > 30:
            return 'hi'
        else:
            return 'en'

    def connect(self):
        """Connect to AWS Transcribe streaming service"""
        try:
            # Start streaming in a new event loop thread
            def run_streaming():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self._start_streaming())
                # Keep the loop running to handle transcription events
                loop.run_forever()
            
            streaming_thread = threading.Thread(target=run_streaming, daemon=True)
            streaming_thread.start()
            
            # Give the streaming thread time to initialize
            time.sleep(1)
            
        except Exception as e:
            error_msg = f"Failed to connect to AWS Transcribe: {e}"
            self.logger.error(error_msg)
            self.on_error(error_msg)
            raise AWSTranscribeError(error_msg)

    def close(self):
        """Close AWS Transcribe streaming connection"""
        try:
            self.is_stream_audio_data = False
            if self.stream and hasattr(self.stream, 'input_stream'):
                # Send end of stream signal using proper event loop context
                import asyncio
                if hasattr(self.user, 'loop') and self.user.loop:
                    try:
                        future = asyncio.run_coroutine_threadsafe(
                            self.stream.input_stream.end_stream(),
                            self.user.loop
                        )
                        # Wait for completion with a timeout to avoid hanging
                        future.result(timeout=5.0)
                    except Exception as close_error:
                        self.logger.warning("Error ending stream: %s", close_error)
                else:
                    self.logger.warning("No event loop available for stream closing")
            self.on_close()
        except Exception as e:
            self.logger.error("Error closing AWS Transcribe stream: %s", e)

    def add_audio_chunk(self, audio_chunk):
        """Add audio chunk to processing queue"""
        self.audio_queue.append(audio_chunk)

    def check_pause_and_handle_transcript(self):
        """Handle transcript processing and conversation flow"""
        greeting = self.user.bot_manager.start_new_call()
        self.last_llm_answer = greeting
        
        # Send greeting using protocol audio
        if hasattr(self.user, 'stream_id') and self.user.stream_id:
            import asyncio
            asyncio.run_coroutine_threadsafe(
                self.request_handler.text_to_protocol_audio_send(greeting),
                self.user.loop
            ).result()
        self.entry_dumper.start_dump_task("llm", greeting)
        
        while self.is_stream_audio_data:
            # Use lock to safely check and process transcript
            with self.transcript_lock:
                should_process = self.last_transcript_time and (time.time() - self.last_transcript_time >= 2)
                if should_process:
                    user_response = self.user_input
                    self.user_input = ""
                    self.last_transcript_time = None
                else:
                    user_response = None
            
            # Process outside the lock to avoid holding it during I/O
            if user_response:
                self.logger.info("Processing final transcript: '%s'", user_response)
                self.request_handler.handle_final_transcript(user_input=user_response)
                with self.transcript_lock:
                    self.last_speak_time = time.time()

            # Check for 30-second timeout with thread safety
            with self.transcript_lock:
                should_repeat = (self.is_completed and self.last_speak_time and 
                               (time.time() - self.last_speak_time >= 30))
            
            if should_repeat:
                if self.last_llm_answer:
                    # Send repeat message using protocol audio
                    if hasattr(self.user, 'stream_id') and self.user.stream_id:
                        import asyncio
                        asyncio.run_coroutine_threadsafe(
                            self.request_handler.text_to_protocol_audio_send(self.last_llm_answer),
                            self.user.loop
                        ).result()
                with self.transcript_lock:
                    self.last_speak_time = time.time()

            time.sleep(0.5)

    def stream_audio_data(self):
        """Stream audio data to AWS Transcribe"""
        audio_buffer = io.BytesIO()
        start_time = time.time()
        max_chunk_size = 1024

        while self.is_stream_audio_data:
            if time.time() - start_time >= 1800:  # 30-minute timeout
                self.logger.info("30-minute timeout reached. Ending transcription.")
                
                llm_answer = "Please try again later. Thank you for using Ongo Service"
                if hasattr(self.user, 'stream_id') and self.user.stream_id:
                    import asyncio
                    asyncio.run_coroutine_threadsafe(
                        self.request_handler.text_to_protocol_audio_send(llm_answer),
                        self.user.loop
                    ).result()
                self.user.sync_send(llm_answer)
                self.user.end_ai_call()
                break

            if self.audio_queue:
                audio_chunk = self.audio_queue.popleft()
                audio_buffer.write(audio_chunk)

                while audio_buffer.tell() >= max_chunk_size:
                    audio_buffer.seek(0)
                    audio_bytes = audio_buffer.read(max_chunk_size)
                    
                    try:
                        if self.stream and hasattr(self.stream, 'input_stream'):
                            # Send audio to AWS Transcribe using proper event loop context
                            import asyncio
                            if hasattr(self.user, 'loop') and self.user.loop:
                                # Send audio to AWS Transcribe using proper event loop context
                                asyncio.run_coroutine_threadsafe(
                                    self.stream.input_stream.send_audio_event(audio_chunk=audio_bytes),
                                    self.user.loop
                                )
                                # Don't wait for result to avoid blocking the streaming thread
                            else:
                                self.logger.warning("No event loop available for audio streaming")
                    except Exception as e:
                        self.logger.error("Error streaming audio to AWS Transcribe: %s", e)
                        self.is_stream_audio_data = False
                        self.close()
                        if hasattr(self.user, 'end_ai_call'):
                            self.user.end_ai_call()
                        return

                    remaining_data = audio_buffer.read()
                    audio_buffer = io.BytesIO()
                    audio_buffer.write(remaining_data)

                audio_buffer.seek(0, io.SEEK_END)
            else:
                time.sleep(0.01)

    def start(self):
        """Start the transcription service"""
        pause_thread = threading.Thread(target=self.check_pause_and_handle_transcript, daemon=True)
        pause_thread.start()
        self.stream_audio_data()
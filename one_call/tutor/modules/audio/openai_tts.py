import io
import os
import asyncio
import openai
from typing import Optional, BinaryIO
from tutor.modules.models.classes import env


class OpenAITTSError(Exception):
    """Custom exception for OpenAI TTS errors"""
    pass


class OpenAITTS:
    """OpenAI Text-to-Speech service with bilingual support"""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize OpenAI TTS client
        
        Args:
            api_key: OpenAI API key. If None, will try to get from environment
        """
        self.api_key = api_key or env.openai_api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise OpenAITTSError("OpenAI API key not provided. Set OPENAI_API_KEY environment variable or configure in settings")
        
        # Store API key for client creation
        
        # Configuration from environment
        self.model = env.openai_tts_model
        self.voice_english = env.openai_tts_voice_english
        self.voice_hindi = env.openai_tts_voice_hindi
        self.format = env.openai_tts_format
        self.speed = env.openai_tts_speed
        self.timeout = env.openai_tts_timeout
    
    def _get_voice_for_language(self, language: str) -> str:
        """Get appropriate voice for detected language
        
        Args:
            language: Language code ('en', 'hi', etc.)
            
        Returns:
            Voice name for the language
        """
        if language == 'hi':
            return self.voice_hindi
        else:
            return self.voice_english  # Default to English voice
    
    async def generate_audio_async(self, text: str, language: str = 'en') -> BinaryIO:
        """Generate audio from text asynchronously with language-specific voice
        
        Args:
            text: Text to convert to speech
            language: Language code ('en', 'hi', etc.)
            
        Returns:
            BytesIO stream containing PCM audio data
        """
        if not text or not text.strip():
            raise OpenAITTSError("Empty text provided for TTS conversion")
        
        try:
            # Select voice based on language
            voice = self._get_voice_for_language(language)
            
            # Create OpenAI client
            client = openai.OpenAI(api_key=self.api_key)
            
            # Make OpenAI TTS API call
            response = await asyncio.to_thread(
                client.audio.speech.create,
                model=self.model,
                voice=voice,
                input=text,
                response_format=self.format,
                speed=self.speed
            )
            
            # Convert response to BytesIO stream
            audio_stream = io.BytesIO()
            
            # Handle different response formats
            if hasattr(response, 'content'):
                audio_stream.write(response.content)
            elif hasattr(response, 'read'):
                audio_stream.write(response.read())
            else:
                # Handle streaming response
                for chunk in response:
                    audio_stream.write(chunk)
            
            audio_stream.seek(0)
            return audio_stream
            
        except Exception as e:
            raise OpenAITTSError(f"Failed to generate audio with OpenAI TTS: {e}")
    
    def generate_audio(self, text: str, language: str = 'en') -> BinaryIO:
        """Generate audio from text synchronously with language-specific voice
        
        Args:
            text: Text to convert to speech
            language: Language code ('en', 'hi', etc.)
            
        Returns:
            BytesIO stream containing PCM audio data
        """
        if not text or not text.strip():
            raise OpenAITTSError("Empty text provided for TTS conversion")
        
        try:
            # Select voice based on language
            voice = self._get_voice_for_language(language)
            
            # Create OpenAI client
            client = openai.OpenAI(api_key=self.api_key)
            
            # Make synchronous OpenAI TTS API call
            response = client.audio.speech.create(
                model=self.model,
                voice=voice,
                input=text,
                response_format=self.format,
                speed=self.speed
            )
            
            # Convert response to BytesIO stream
            audio_stream = io.BytesIO()
            
            # Handle response content
            if hasattr(response, 'content'):
                audio_stream.write(response.content)
            elif hasattr(response, 'read'):
                audio_stream.write(response.read())
            else:
                # Handle streaming response
                for chunk in response:
                    audio_stream.write(chunk)
            
            audio_stream.seek(0)
            return audio_stream
            
        except Exception as e:
            raise OpenAITTSError(f"Failed to generate audio with OpenAI TTS: {e}")


# Global TTS instance
tts_instance = None

def get_tts_instance() -> OpenAITTS:
    """Get or create global OpenAI TTS instance"""
    global tts_instance
    if tts_instance is None:
        tts_instance = OpenAITTS()
    return tts_instance


# Compatibility functions for existing code
async def text_to_audio(text: str, language: str = 'en') -> BinaryIO:
    """Convert text to audio with language support (async)
    
    Args:
        text: Text to convert to speech
        language: Language code ('en', 'hi', etc.)
        
    Returns:
        BytesIO stream containing PCM audio data at 44100Hz, 16-bit, mono
    """
    tts = get_tts_instance()
    return await tts.generate_audio_async(text, language)


def synthesize_speech(text: str, language: str = 'en') -> str:
    """Generate speech file and return file path
    
    Args:
        text: Text to convert to speech
        language: Language code ('en', 'hi', etc.)
        
    Returns:
        Path to generated audio file
    """
    try:
        tts = get_tts_instance()
        audio_stream = tts.generate_audio(text, language)
        
        # Save to temporary file
        from tempfile import gettempdir
        output = os.path.join(gettempdir(), f"speech_{language}.mp3")
        
        with open(output, "wb") as file:
            file.write(audio_stream.read())
        
        return output
        
    except Exception as e:
        raise OpenAITTSError(f"Failed to synthesize speech: {e}")
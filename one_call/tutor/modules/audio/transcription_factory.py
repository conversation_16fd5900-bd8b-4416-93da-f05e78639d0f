"""
Transcription Service Factory

Factory pattern implementation for creating transcription service instances.
Supports both AssemblyAI and Google Cloud Speech with configurable fallback.
"""
import time
import os
from typing import Optional, Union
from tutor.modules.models import models
from tutor.modules.logger import logger


class TranscriptionServiceError(Exception):
    """Custom exception for transcription service errors"""
    pass


class TranscriptionFactory:
    """Factory class for creating transcription service instances"""
    
    @staticmethod
    def create_transcription_service(user, logger_instance, 
                                   service_type: Optional[str] = None,
                                   enable_fallback: bool = True):
        """
        Create a transcription service instance based on configuration.
        
        Args:
            user: User instance
            logger_instance: Logger instance
            service_type: Override service type ("assemblyai", "google_cloud", or "aws_transcribe")
            enable_fallback: Enable fallback to alternative service if primary fails
            
        Returns:
            RealtimeTranscription instance
            
        Raises:
            TranscriptionServiceError: If no service can be created
        """
        # Determine service type
        primary_service = service_type or models.env.transcription_service
        fallback_enabled = enable_fallback and models.env.transcription_fallback_enabled
        
        # Try primary service
        try:
            transcription_service = TranscriptionFactory._create_service(
                primary_service, user, logger_instance
            )
            logger_instance.info(f"Created {primary_service} transcription service")
            return transcription_service
            
        except Exception as e:
            logger_instance.error(f"Failed to create {primary_service} service: {e}")
            
            if not fallback_enabled:
                raise TranscriptionServiceError(f"Failed to create {primary_service} service and fallback is disabled")
            
            # Try fallback service with smart selection
            if primary_service == "openai_whisper":
                fallback_service = "assemblyai"  # Best English fallback
            elif primary_service == "assemblyai":
                fallback_service = "openai_whisper"  # Better multilingual support
            elif primary_service == "aws_transcribe":
                fallback_service = "openai_whisper"  # Better multilingual support
            else:
                fallback_service = "assemblyai"
            
            try:
                logger_instance.warning(f"Attempting fallback to {fallback_service}")
                transcription_service = TranscriptionFactory._create_service(
                    fallback_service, user, logger_instance
                )
                logger_instance.info(f"Successfully created fallback {fallback_service} transcription service")
                return transcription_service
                
            except Exception as fallback_error:
                logger_instance.error(f"Failed to create fallback {fallback_service} service: {fallback_error}")
                raise TranscriptionServiceError(
                    f"Failed to create both {primary_service} and {fallback_service} services"
                )

    @staticmethod
    def _create_service(service_type: str, user, logger_instance):
        """
        Internal method to create a specific transcription service.
        
        Args:
            service_type: "assemblyai", "google_cloud", or "aws_transcribe"
            user: User instance
            logger_instance: Logger instance
            
        Returns:
            RealtimeTranscription instance
            
        Raises:
            TranscriptionServiceError: If service creation fails
        """
        if service_type == "assemblyai":
            return TranscriptionFactory._create_assemblyai_service(user, logger_instance)
        elif service_type == "aws_transcribe":
            return TranscriptionFactory._create_aws_transcribe_service(user, logger_instance)
        else:
            raise TranscriptionServiceError(f"Unknown transcription service: {service_type}")

    @staticmethod
    def _create_assemblyai_service(user, logger_instance):
        """Create AssemblyAI transcription service"""
        try:
            from tutor.modules.audio.assembly_ai.realtime_transcription import RealtimeTranscription
            
            api_key = models.env.assemblyai_api_key
            if not api_key:
                raise TranscriptionServiceError("AssemblyAI API key not configured")
            
            # Create transcription service instance with Hindi support
            transcription = RealtimeTranscription(
                api_key=api_key,
                user=user,
                logger=logger_instance,
                sample_rate=16000,
                language_code=models.env.assemblyai_language_code,
                language_options=models.env.assemblyai_language_options,
                enable_auto_detection=models.env.assemblyai_enable_auto_detection,
                speaker_diarization=models.env.assemblyai_speaker_diarization,
                confidence_threshold=models.env.assemblyai_confidence_threshold
            )
            
            return transcription
            
        except ImportError as e:
            raise TranscriptionServiceError(f"AssemblyAI module not available: {e}")
        except Exception as e:
            raise TranscriptionServiceError(f"Failed to create AssemblyAI service: {e}")

    @staticmethod
    def _create_aws_transcribe_service(user, logger_instance):
        """Create AWS Transcribe transcription service"""
        try:
            from tutor.modules.audio.aws_transcribe.realtime_transcription import RealtimeTranscription

            region = models.env.aws_transcribe_region
            access_key_id = models.env.aws_transcribe_access_key_id
            secret_access_key = models.env.aws_transcribe_secret_access_key

            if not access_key_id or not secret_access_key:
                raise TranscriptionServiceError("AWS Transcribe credentials not configured")

            # Create transcription service instance
            transcription = RealtimeTranscription(
                region=region,
                access_key_id=access_key_id,
                secret_access_key=secret_access_key,
                user=user,
                logger=logger_instance,
                sample_rate=models.env.aws_transcribe_sample_rate,
                language_code=models.env.aws_transcribe_language_code
            )

            return transcription

        except ImportError as e:
            raise TranscriptionServiceError(f"AWS Transcribe module not available: {e}")
        except Exception as e:
            raise TranscriptionServiceError(f"Failed to create AWS Transcribe service: {e}")

    @staticmethod
    def get_available_services():
        """
        Get list of available transcription services.
        
        Returns:
            List of available service names
        """
        available = []
        
        # Check AssemblyAI availability
        try:
            import assemblyai as aai
            if models.env.assemblyai_api_key:
                available.append("assemblyai")
        except ImportError:
            pass
            
        # Check Google Cloud Speech availability
        try:
            from google.cloud import speech
            if models.env.google_cloud_project_id:
                available.append("google_cloud")
        except ImportError:
            pass

        # Check AWS Transcribe availability
        try:
            import boto3
            if models.env.aws_transcribe_access_key_id and models.env.aws_transcribe_secret_access_key:
                available.append("aws_transcribe")
        except ImportError:
            pass

        # Check OpenAI Whisper availability
        try:
            import openai
            if models.env.openai_api_key:
                available.append("openai_whisper")
        except ImportError:
            pass

        return available

    @staticmethod
    def validate_service_config(service_type: str) -> tuple[bool, str]:
        """
        Validate configuration for a specific service.
        
        Args:
            service_type: Service to validate ("assemblyai", "google_cloud", or "aws_transcribe")
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if service_type == "assemblyai":
            if not models.env.assemblyai_api_key:
                return False, "AssemblyAI API key not configured"
            try:
                import assemblyai as aai
                return True, ""
            except ImportError:
                return False, "AssemblyAI library not installed"
                
        elif service_type == "google_cloud":
            if not models.env.google_cloud_project_id:
                return False, "Google Cloud project ID not configured"
            try:
                from google.cloud import speech
                return True, ""
            except ImportError:
                return False, "Google Cloud Speech library not installed"

        elif service_type == "aws_transcribe":
            if not models.env.aws_transcribe_access_key_id or not models.env.aws_transcribe_secret_access_key:
                return False, "AWS Transcribe credentials not configured"
            try:
                import boto3
                return True, ""
            except ImportError:
                return False, "AWS Transcribe libraries not installed"
        elif service_type == "openai_whisper":
            if not models.env.openai_api_key:
                return False, "OpenAI API key not configured"
            try:
                import openai
                return True, ""
            except ImportError:
                return False, "OpenAI library not installed"
        else:
            return False, f"Unknown service type: {service_type}"

    @staticmethod
    def create_with_retry(user, logger_instance, max_retries: int = 3, 
                         retry_delay: float = 1.0):
        """
        Create transcription service with retry logic.
        
        Args:
            user: User instance
            logger_instance: Logger instance
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
            
        Returns:
            RealtimeTranscription instance
            
        Raises:
            TranscriptionServiceError: If all retries fail
        """
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                return TranscriptionFactory.create_transcription_service(user, logger_instance)
            except Exception as e:
                last_error = e
                if attempt < max_retries:
                    logger_instance.warning(f"Transcription service creation failed (attempt {attempt + 1}), retrying in {retry_delay}s: {e}")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger_instance.error(f"All transcription service creation attempts failed")
        
        raise TranscriptionServiceError(f"Failed to create transcription service after {max_retries + 1} attempts. Last error: {last_error}")

    @staticmethod
    def check_service_health(service_type: str) -> tuple[bool, str]:
        """
        Check if a transcription service is healthy and ready to use.
        
        Args:
            service_type: Service to check ("assemblyai", "google_cloud", or "aws_transcribe")
            
        Returns:
            Tuple of (is_healthy, status_message)
        """
        try:
            if service_type == "assemblyai":
                # Check AssemblyAI health
                import assemblyai as aai
                if not models.env.assemblyai_api_key:
                    return False, "AssemblyAI API key not configured"
                
                # Test API connectivity (basic validation)
                try:
                    aai.settings.api_key = models.env.assemblyai_api_key
                    # Simple check - if the library loads and key is set, service should be available
                    return True, "AssemblyAI service is healthy"
                except Exception as e:
                    return False, f"AssemblyAI API error: {e}"
                    
            elif service_type == "google_cloud":
                # Check Google Cloud Speech health
                from google.cloud import speech
                
                if not models.env.google_cloud_project_id:
                    return False, "Google Cloud project ID not configured"
                
                # Check credentials
                if models.env.google_cloud_credentials_path:
                    if not os.path.exists(models.env.google_cloud_credentials_path):
                        return False, f"Google Cloud credentials file not found: {models.env.google_cloud_credentials_path}"
                else:
                    # Check if default credentials are available
                    try:
                        import google.auth
                        credentials, project = google.auth.default()
                        if not project:
                            return False, "No Google Cloud project found in default credentials"
                    except Exception as e:
                        return False, f"Google Cloud authentication error: {e}"
                
                # Test basic client creation
                try:
                    if models.env.google_cloud_credentials_path:
                        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = models.env.google_cloud_credentials_path
                    client = speech.SpeechClient()
                    return True, "Google Cloud Speech service is healthy"
                except Exception as e:
                    return False, f"Google Cloud Speech client error: {e}"
                    
            elif service_type == "aws_transcribe":
                # Check AWS Transcribe health
                if not models.env.aws_transcribe_access_key_id or not models.env.aws_transcribe_secret_access_key:
                    return False, "AWS Transcribe credentials not configured"
                
                try:
                    import boto3
                    from amazon_transcribe.client import TranscribeStreamingClient
                    
                    # Test basic client creation
                    client = TranscribeStreamingClient(region=models.env.aws_transcribe_region)
                    return True, "AWS Transcribe service is healthy"
                except ImportError as e:
                    return False, f"AWS Transcribe libraries not available: {e}"
                except Exception as e:
                    return False, f"AWS Transcribe client error: {e}"
                    
            elif service_type == "openai_whisper":
                # Check OpenAI Whisper health
                if not models.env.openai_api_key:
                    return False, "OpenAI API key not configured"
                
                try:
                    import openai
                    
                    # Test basic client creation
                    client = openai.OpenAI(api_key=models.env.openai_api_key)
                    return True, "OpenAI Whisper service is healthy"
                except ImportError as e:
                    return False, f"OpenAI library not available: {e}"
                except Exception as e:
                    return False, f"OpenAI Whisper client error: {e}"
            else:
                return False, f"Unknown service type: {service_type}"
                
        except ImportError as e:
            return False, f"Required library not available: {e}"
        except Exception as e:
            return False, f"Health check failed: {e}"

    @staticmethod
    def get_fallback_service(primary_service: str) -> Optional[str]:
        """
        Get the fallback service for a given primary service.
        
        Args:
            primary_service: The primary service name
            
        Returns:
            Fallback service name or None if no fallback available
        """
        if not models.env.transcription_fallback_enabled:
            return None
            
        fallback_map = {
            "openai_whisper": "assemblyai",     # Whisper -> AssemblyAI (English optimized)
            "assemblyai": "openai_whisper",     # AssemblyAI -> Whisper (multilingual)
            "aws_transcribe": "openai_whisper", # AWS -> Whisper (better multilingual)
            "google_cloud": "openai_whisper"    # Google -> Whisper (better multilingual)
        }
        
        fallback = fallback_map.get(primary_service)
        if fallback:
            # Check if fallback service is available and healthy
            is_healthy, _ = TranscriptionFactory.check_service_health(fallback)
            if is_healthy:
                return fallback
                
        return None


# Convenience function for backward compatibility
def create_realtime_transcription(user, logger_instance, service_type: Optional[str] = None):
    """
    Convenience function to create a transcription service.
    Maintains backward compatibility with existing code.
    """
    return TranscriptionFactory.create_transcription_service(user, logger_instance, service_type)
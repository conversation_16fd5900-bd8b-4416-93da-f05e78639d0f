import io
import threading
import time
from collections import deque

import assemblyai as aai  # Assuming aai is the relevant module for the transcription API

from tutor.executors.entry_dumper import EntryDumper
from tutor.executors.request_handler import RequestHandler
# from tutor.modules.preset_vehicle.request_handler import RequestHandler


class RealtimeTranscription:
    def __init__(self, api_key, user, logger, sample_rate=16000, 
                 language_code="auto", language_options=None, 
                 enable_auto_detection=True, speaker_diarization=True,
                 confidence_threshold=0.75):
        self.user = user
        self.logger = logger
        self.session = user.session
        self.file_name = "realtime_transcription.yaml"
        self.api_key = api_key
        self.sample_rate = sample_rate
        
        # Language detection configuration
        self.language_code = language_code
        self.language_options = language_options or ["hi", "en"]
        self.enable_auto_detection = enable_auto_detection
        self.speaker_diarization = speaker_diarization
        self.confidence_threshold = confidence_threshold
        
        # Language detection state
        self.detected_language = None
        self.detected_language_code = None
        self.language_confidence = 0.0
        self.audio_queue = deque()
        self.listen_task_threading = threading.Thread(target=self.stream_audio_data, daemon=True)
        self.is_stream_audio_data = True
        self.realtime_final_transcript = None
        self.last_transcript_time = None  # Initialize last_transcript_time
        aai.settings.api_key = self.api_key
        
        # Configure transcriber with language detection settings
        transcriber_config = {
            'sample_rate': self.sample_rate,
            'on_data': self.on_data,
            'on_error': self.on_error,
            'on_open': self.on_open,
            'on_close': self.on_close,
            'on_extra_session_information': self.on_extra_session_information
        }
        
        # Add language detection if enabled
        if self.enable_auto_detection and self.language_code == "auto":
            self.logger.info("AssemblyAI configured for auto-language detection: %s", self.language_options)
            # AssemblyAI will auto-detect language - no specific parameter needed
            # The service will automatically identify Hindi, English, etc.
        else:
            # Use specific language if not auto-detection
            if self.language_code != "auto":
                self.logger.info("AssemblyAI configured for specific language: %s", self.language_code)
                # Note: AssemblyAI handles language detection internally
        
        self.transcriber = aai.RealtimeTranscriber(**transcriber_config)

        self.is_completed = True
        self.user_input = ""
        self.last_speak_time = time.time()
        self.last_llm_answer = None
        self.conversation_history = []
        self.entry_dumper = EntryDumper(self.user.mobile, self.session, self.file_name)
        self.request_handler = RequestHandler(self.user, self.entry_dumper, logger)

    def on_open(self, session_opened: aai.RealtimeSessionOpened):
         self.logger.info("AssemblyAI Session ID: %s", session_opened.session_id)

    def on_data(self, transcript: aai.RealtimeTranscript):
        if not transcript.text:
            return
        
        current_time = time.time()
        
        # Perform client-side language detection for better accuracy
        detected_lang = self._detect_language_from_text(transcript.text)
        if detected_lang != self.detected_language:
            self.detected_language = detected_lang
            self.detected_language_code = f"{detected_lang}-{'IN' if detected_lang == 'hi' else 'US'}"
            self.logger.info("Client-side detected language: %s (%s)", 
                            self.detected_language, self.detected_language_code)
            # Store language in user session
            if hasattr(self.user, 'detected_language'):
                self.user.detected_language = self.detected_language
            # Store in entry dumper for session tracking
            self.entry_dumper.start_dump_task("detected_language", 
                                             f"{self.detected_language} (AssemblyAI)")
        
        if isinstance(transcript, aai.RealtimeFinalTranscript):
            lang_info = f"[{self.detected_language or 'auto'}]" if self.detected_language else ""
            self.logger.info("AssemblyAI FinalTranscript %s: %s", lang_info, transcript.text)
            self.user_input += transcript.text
        else:
            lang_info = f"[{self.detected_language or 'auto'}]" if self.detected_language else ""
            self.logger.info("AssemblyAI PartialTranscript %s: %s", lang_info, transcript.text)
            self.request_handler.handle_partial_transcript(user_input=transcript.text)

        self.last_transcript_time = current_time  # Update the last transcript time
        self.last_speak_time = time.time()

    def check_pause_and_handle_transcript(self):
        #greeting = self.user.vehicle_validator.assistant.start_new_call()
        greeting = self.user.bot_manager.start_new_call()
        self.last_llm_answer = greeting
        # Send greeting using protocol audio
        if hasattr(self.user, 'stream_id') and self.user.stream_id:
            import asyncio
            asyncio.run_coroutine_threadsafe(
                self.request_handler.text_to_protocol_audio_send(greeting),
                self.user.loop
            ).result()
        self.entry_dumper.start_dump_task("llm", greeting)
        while self.is_stream_audio_data:
            if self.last_transcript_time and (time.time() - self.last_transcript_time >= 2):
                user_response = self.user_input
                self.user_input = ""
                self.request_handler.handle_final_transcript(user_input=user_response)
                # self.handle_final_transcript_from_transcription(user_response)
                self.last_transcript_time = None
                self.last_speak_time = time.time()

            if self.is_completed and self.last_speak_time and (time.time() - self.last_speak_time >= 30):
                if self.last_llm_answer:
                    # Send repeat message using protocol audio
                    if hasattr(self.user, 'stream_id') and self.user.stream_id:
                        import asyncio
                        asyncio.run_coroutine_threadsafe(
                            self.request_handler.text_to_protocol_audio_send(self.last_llm_answer),
                            self.user.loop
                        ).result()

                self.last_speak_time = time.time()

            time.sleep(0.5)

    def on_error(self, error: aai.RealtimeError):
        # print("An error occurred:", error)
        self.logger.error("An error occurred: %s", error)
        
        # Handle specific error types that require cleanup
        error_message = str(error)
        if "idle for too long" in error_message.lower() or "session" in error_message.lower():
            self.logger.info("Session error detected, triggering cleanup")
            try:
                # Stop the transcription stream
                self.is_stream_audio_data = False
                # End the user's AI call to trigger proper cleanup
                if hasattr(self.user, 'end_ai_call'):
                    self.user.end_ai_call()
            except Exception as cleanup_error:
                self.logger.error("Error during cleanup: %s", cleanup_error)

    def on_close(self):
        # print("Closing Session")
        self.logger.info("Closing Session")

    def connect(self):
        self.transcriber.connect()

    def close(self):
        self.is_stream_audio_data = False
        self.transcriber.close()

    def on_extra_session_information(self, data: aai.RealtimeSessionInformation):
        self.entry_dumper.start_dump_task("audio_duration_seconds", f"{data.audio_duration_seconds} seconds")
        # print(f"on_extra_session_information : {data.audio_duration_seconds} seconds")
        self.logger.info("on_extra_session_information: %s seconds", data.audio_duration_seconds)

    def add_audio_chunk(self, audio_chunk):
        self.audio_queue.append(audio_chunk)

    def stream_audio_data(self):
        audio_buffer = io.BytesIO()
        start_time = time.time()
        max_chunk_size = 1024

        while self.is_stream_audio_data:
            if time.time() - start_time >= 1800:
                # print("3-minute timeout reached. Sending transcripts and exiting.")
                self.logger.info("3-minute timeout reached. Sending transcripts and exiting.")
               
                llm_answer = "Please try again later. Thank you for using Ongo Service"
                # Send timeout message using protocol audio
                if hasattr(self.user, 'stream_id') and self.user.stream_id:
                    import asyncio
                    asyncio.run_coroutine_threadsafe(
                        self.request_handler.text_to_protocol_audio_send(llm_answer),
                        self.user.loop
                    ).result()
                self.user.sync_send(llm_answer)
                self.user.end_ai_call()
                break

            if self.audio_queue:
                audio_chunk = self.audio_queue.popleft()
                audio_buffer.write(audio_chunk)

                while audio_buffer.tell() >= max_chunk_size:
                    audio_buffer.seek(0)
                    audio_bytes = audio_buffer.read(max_chunk_size)
                    try:
                        self.transcriber.stream(audio_bytes)
                    except Exception as e:
                        # print(f"Error streaming audio: {e}")
                        self.logger.error("Error streaming audio: %s", e)
                        # Stop the stream and trigger cleanup
                        self.is_stream_audio_data = False
                        self.close()
                        # Trigger user cleanup to remove from active users
                        if hasattr(self.user, 'end_ai_call'):
                            self.user.end_ai_call()
                        return

                    remaining_data = audio_buffer.read()
                    audio_buffer = io.BytesIO()
                    audio_buffer.write(remaining_data)

                audio_buffer.seek(0, io.SEEK_END)
            else:
                time.sleep(0.01)

    def _detect_language_from_text(self, text: str) -> str:
        """Detect language from text using character analysis
        
        Args:
            text: The text to analyze for language detection
            
        Returns:
            Language code ('hi' for Hindi, 'en' for English)
        """
        if not text or not text.strip():
            return 'en'  # Default to English for empty text
        
        # Count Devanagari characters (Hindi script)
        devanagari_chars = 0
        latin_chars = 0
        total_chars = 0
        
        for char in text:
            if char.isalpha():
                total_chars += 1
                # Check if character is in Devanagari range (Hindi)
                if 0x0900 <= ord(char) <= 0x097F:
                    devanagari_chars += 1
                # Check if character is in Latin range (English)
                elif 0x0041 <= ord(char) <= 0x007A:
                    latin_chars += 1
        
        if total_chars == 0:
            return 'en'  # Default to English if no alphabetic characters
        
        # Calculate percentages
        devanagari_percentage = (devanagari_chars / total_chars) * 100
        
        # If more than 30% of characters are Devanagari, consider it Hindi
        if devanagari_percentage > 30:
            return 'hi'
        else:
            return 'en'

    def start(self):
        pause_thread = threading.Thread(target=self.check_pause_and_handle_transcript, daemon=True)
        pause_thread.start()
        self.stream_audio_data()


"""

    def handle_final_transcript_from_transcription(self, user_input):
        while True:
            self.is_completed = False
            self.entry_dumper.start_dump_task("user", user_input)
            user_input = word_to_number.remove_symbols_except_period(user_input)
            self.user.sync_send(json.dumps({"type": "transcript_batch", "data": user_input}))
            print("Pausing audio recording...")
            self.user.sync_send(json.dumps({"type": "pause_audio_recording", "data": ""}))
            print("user input:", user_input)
            is_amount = False
            llm_answer = None
            end_call = False

            vehicle_number_str = self.user.vehicle_validator.vehicle_number_str
            if vehicle_number_str:
                user_amount_str, is_amount = self.user.vehicle_validator.get_amount(user_input)
                if is_amount:
                    user_input = user_amount_str
                else:
                    llm_answer = user_amount_str
            else:
                vehicle_number_str, is_found_vehicle = self.user.vehicle_validator.assistant.get_user_vehicle_number(
                    user_input)
                if is_found_vehicle:
                    self.user.vehicle_validator.vehicle_number_str = vehicle_number_str
                    user_input = vehicle_number_str

            if llm_answer:
                print("user_amount_str ai response:", llm_answer)
                self.entry_dumper.start_dump_task("user_amount_str", llm_answer)
            else:
                start_time = time.time()
                llm_answer = self.user.vehicle_validator.assistant.answer_call_back(user_input)
                end_time = time.time()
                response_time = end_time - start_time
                print("Response time of bot.answer_call_back:", response_time)
                print("ai response:", llm_answer)
                self.entry_dumper.start_dump_task("llm", f"{llm_answer}, response_time {response_time}")

                status, result, is_phrase, preset_amount_response = self.user.vehicle_validator.is_vehicle_number_and_preset_amount_complete(
                    llm_answer)
                if status:
                    static_response = static_responses.sending_user_request()
                    self.text_to_audio_chunk_send(static_response)
                    self.entry_dumper.start_dump_task("static_response", f"{static_response}")

                    llm_answer = self.user.vehicle_validator.post_vehicle_number_and_preset_amount(llm_answer, result,
                                                                                                   is_phrase)
                    self.entry_dumper.start_dump_task("api_response", f"{llm_answer}")
                    if "error:" in llm_answer:
                        llm_answer = llm_answer.replace("error:", "")
                    else:
                        end_call = True
                elif preset_amount_response:
                    llm_answer = preset_amount_response

            print("ai after validation response:", llm_answer)
            llm_answer = format_vehicle_number(llm_answer, self.user.vehicle_validator.vehicle_number_list)
            self.text_to_audio_chunk_send(llm_answer)
            self.last_llm_answer = llm_answer
            self.entry_dumper.start_dump_task("after_validation", f"{llm_answer}")
            if end_call:
                self.user.send_end_call()
            else:
                self.is_completed = True
            break

    def text_to_audio_chunk_send(self, text):
        async def send_audio():
            self.user.ai_start_listening = False
            self.user.audio_chunk_is_send = False

            audio_stream = self.user.audio_processor.tts.generate_audio(text)
            buffer = io.BytesIO()
            audio_chunk_size = 512
            try:
                while True:
                    audio_chunk = audio_stream.read(audio_chunk_size)
                    if not audio_chunk:
                        break
                    buffer.write(audio_chunk)
                    if buffer.tell() >= audio_chunk_size:
                        buffer.seek(0)
                        audio_chunk_base64 = base64.b64encode(buffer.read(audio_chunk_size)).decode('utf-8')
                        await self.sync_send_async(json.dumps({"type": "llm_answer", "data": audio_chunk_base64}))
                        buffer.seek(0)
                        buffer.truncate(0)
                if buffer.tell() > 0:
                    buffer.seek(0)
                    audio_chunk_base64 = base64.b64encode(buffer.read()).decode('utf-8')
                    await self.sync_send_async(json.dumps({"type": "llm_answer", "data": audio_chunk_base64}))
            finally:
                self.user.audio_chunk_is_send = True

        user_loop = self.user.loop
        future = asyncio.run_coroutine_threadsafe(send_audio(), user_loop)
        future.result()

    async def sync_send_async(self, message):
        await self.user.send(message)

    def handle_partial_transcript_from_transcription(self, user_input):
        self.user.sync_send(json.dumps({"type": "transcript_batch", "data": user_input}))
"""

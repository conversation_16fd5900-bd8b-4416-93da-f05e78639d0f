"""
OpenAI Whisper Real-time Transcription Module

This module provides real-time speech-to-text transcription using OpenAI's Whisper API.
It implements a chunked processing approach to provide near real-time transcription
for streaming audio applications.

Features:
- Chunked audio processing with configurable overlap
- Native multilingual support (Hindi/English)
- Automatic language detection
- Error handling and retry mechanisms
- Thread-safe operations
- Integration with existing transcription factory

Author: AI Assistant
Date: 2025-08-23
"""

from .realtime_transcription import RealtimeTranscription

__all__ = ['RealtimeTranscription']
__version__ = '1.0.0'

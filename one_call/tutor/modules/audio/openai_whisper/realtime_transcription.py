import asyncio
import io
import threading
import time
import uuid
import wave
import tempfile
import os
from collections import deque
from typing import Optional, Dict, Any

import openai

from tutor.executors.entry_dumper import EntryDumper
from tutor.executors.request_handler import RequestHandler


class OpenAIWhisperError(Exception):
    """Custom exception for OpenAI Whisper errors"""
    pass


class RealtimeTranscription:
    """OpenAI Whisper real-time transcription service using chunked processing"""
    
    def __init__(self, api_key, user, logger, sample_rate=16000, 
                 language_code="auto", language_options=None, 
                 model="whisper-1", chunk_duration=5.0, overlap_duration=1.0,
                 temperature=0.0, timeout=30, max_retries=3, retry_delay=1.0,
                 prompt=None):
        self.user = user
        self.logger = logger
        self.session = user.session
        self.file_name = "realtime_transcription.yaml"
        
        # OpenAI Whisper configuration
        self.api_key = api_key
        self.model = model
        self.language_code = language_code
        self.language_options = language_options or ["hi", "en"]
        self.sample_rate = sample_rate
        self.chunk_duration = chunk_duration
        self.overlap_duration = overlap_duration
        self.temperature = temperature
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.prompt = prompt
        
        # Language detection state
        self.detected_language = None
        self.detected_language_code = None
        
        # Audio processing
        self.audio_queue = deque()
        self.listen_task_threading = threading.Thread(target=self.stream_audio_data, daemon=True)
        self.is_stream_audio_data = True
        self.realtime_final_transcript = None
        self.last_transcript_time = None
        
        # Thread synchronization
        self.transcript_lock = threading.Lock()
        
        # Transcription state
        self.is_completed = True
        self.user_input = ""
        self.last_speak_time = time.time()
        self.last_llm_answer = None
        self.conversation_history = []
        
        # OpenAI client
        self.client: Optional[openai.OpenAI] = None
        self.session_id = f"whisper_{int(time.time())}"
        
        # Audio chunking
        self.audio_buffer = io.BytesIO()
        self.chunk_size_bytes = int(self.sample_rate * 2 * self.chunk_duration)  # 16-bit audio
        self.overlap_size_bytes = int(self.sample_rate * 2 * self.overlap_duration)
        self.last_chunk_overlap = b""
        
        # Integration components
        self.entry_dumper = EntryDumper(self.user.mobile, self.session, self.file_name)
        self.request_handler = RequestHandler(self.user, self.entry_dumper, logger)
        
        # Initialize OpenAI client
        self._initialize_client()

    def _initialize_client(self):
        """Initialize OpenAI client"""
        try:
            if not self.api_key:
                raise OpenAIWhisperError("OpenAI API key is required")

            self.client = openai.OpenAI(api_key=self.api_key)
            self.logger.info("OpenAI Whisper client initialized successfully")
            self.logger.info("OpenAI Whisper configured: model=%s, language=%s, chunk_duration=%.1fs, overlap=%.1fs, temperature=%.1f",
                           self.model, self.language_code, self.chunk_duration, self.overlap_duration, self.temperature)
            if self.prompt:
                self.logger.info("OpenAI Whisper multilingual prompt: %s", self.prompt)
        except Exception as e:
            error_msg = f"Failed to initialize OpenAI Whisper client: {e}"
            self.logger.error(error_msg)
            raise OpenAIWhisperError(error_msg)

    def on_open(self):
        """Called when transcription session opens (compatibility method)"""
        self.logger.info("OpenAI Whisper Session ID: %s", self.session_id)

    def on_data(self, transcript_text: str, is_final: bool = False):
        """Handle transcription data (compatibility method)"""
        if not transcript_text:
            return
        
        current_time = time.time()

        if is_final:
            self.logger.info("OpenAI Whisper FinalTranscript: %s", transcript_text)
            with self.transcript_lock:
                self.user_input += transcript_text
                self.last_transcript_time = current_time
                self.last_speak_time = time.time()
        else:
            self.logger.info("OpenAI Whisper PartialTranscript: %s", transcript_text)
            self.request_handler.handle_partial_transcript(user_input=transcript_text)
            with self.transcript_lock:
                self.last_speak_time = time.time()

    def on_error(self, error):
        """Handle transcription errors (compatibility method)"""
        self.logger.error("OpenAI Whisper error: %s", error)
        
        error_message = str(error)
        if "rate limit" in error_message.lower() or "quota" in error_message.lower():
            self.logger.info("Rate limit or quota error detected, triggering cleanup")
            try:
                self.is_stream_audio_data = False
                if hasattr(self.user, 'end_ai_call'):
                    self.user.end_ai_call()
            except Exception as cleanup_error:
                self.logger.error("Error during cleanup: %s", cleanup_error)

    def on_close(self):
        """Called when transcription session closes (compatibility method)"""
        self.logger.info("OpenAI Whisper Session Closed")

    def on_extra_session_information(self, data: Dict[str, Any]):
        """Handle extra session information (compatibility method)"""
        duration = data.get('audio_duration_seconds', 0)
        self.entry_dumper.start_dump_task("audio_duration_seconds", f"{duration} seconds")
        self.logger.info("Session information: %s seconds", duration)

    def _create_audio_file(self, audio_data: bytes) -> str:
        """Create a temporary WAV file from audio data"""
        try:
            # Create temporary file
            temp_fd, temp_path = tempfile.mkstemp(suffix='.wav')
            
            with os.fdopen(temp_fd, 'wb') as temp_file:
                # Write WAV header and audio data
                with wave.open(temp_file, 'wb') as wav_file:
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(self.sample_rate)
                    wav_file.writeframes(audio_data)
            
            return temp_path
        except Exception as e:
            self.logger.error("Error creating audio file: %s", e)
            raise OpenAIWhisperError(f"Failed to create audio file: {e}")

    def _transcribe_audio_chunk(self, audio_data: bytes) -> Optional[str]:
        """Transcribe a single audio chunk using OpenAI Whisper"""
        if len(audio_data) < 1024:  # Skip very small chunks
            return None
        
        temp_path = None
        try:
            # Create temporary audio file
            temp_path = self._create_audio_file(audio_data)
            
            # Prepare transcription parameters
            transcribe_params = {
                'model': self.model,
                'file': open(temp_path, 'rb'),
                'response_format': 'text',
                'temperature': self.temperature
            }
            
            # Add language if not auto-detection
            if self.language_code != "auto":
                transcribe_params['language'] = self.language_code
            
            # Add prompt if provided
            if self.prompt:
                transcribe_params['prompt'] = self.prompt
            
            # Make API call with retries
            for attempt in range(self.max_retries):
                try:
                    if not self.client:
                        raise OpenAIWhisperError("OpenAI client not initialized")
                    response = self.client.audio.transcriptions.create(**transcribe_params)
                    
                    # Close file handle
                    transcribe_params['file'].close()
                    
                    if response and hasattr(response, 'text') and response.text.strip():
                        return response.text.strip()
                    return None
                    
                except Exception as e:
                    transcribe_params['file'].close()
                    if attempt < self.max_retries - 1:
                        self.logger.warning("Transcription attempt %d failed, retrying: %s", attempt + 1, e)
                        time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                        # Reopen file for next attempt
                        transcribe_params['file'] = open(temp_path, 'rb')
                    else:
                        self.logger.error("All transcription attempts failed: %s", e)
                        raise OpenAIWhisperError(f"Transcription failed after {self.max_retries} attempts: {e}")
            
        except Exception as e:
            self.logger.error("Error transcribing audio chunk: %s", e)
            return None
        finally:
            # Clean up temporary file
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                except Exception as cleanup_error:
                    self.logger.warning("Failed to cleanup temp file %s: %s", temp_path, cleanup_error)



    def connect(self):
        """Connect to OpenAI Whisper service (compatibility method)"""
        try:
            # Test the connection by making a simple API call
            if self.client:
                self.on_open()
                self.logger.info("OpenAI Whisper service connected successfully")
            else:
                raise OpenAIWhisperError("OpenAI client not initialized")
        except Exception as e:
            error_msg = f"Failed to connect to OpenAI Whisper: {e}"
            self.logger.error(error_msg)
            self.on_error(error_msg)
            raise OpenAIWhisperError(error_msg)

    def close(self):
        """Close OpenAI Whisper connection (compatibility method)"""
        try:
            self.is_stream_audio_data = False
            self.on_close()
        except Exception as e:
            self.logger.error("Error closing OpenAI Whisper connection: %s", e)

    def add_audio_chunk(self, audio_chunk):
        """Add audio chunk to processing queue"""
        self.audio_queue.append(audio_chunk)

    def check_pause_and_handle_transcript(self):
        """Handle transcript processing and conversation flow"""
        greeting = self.user.bot_manager.start_new_call()
        self.last_llm_answer = greeting

        # Send greeting using protocol audio
        if hasattr(self.user, 'stream_id') and self.user.stream_id:
            import asyncio
            asyncio.run_coroutine_threadsafe(
                self.request_handler.text_to_protocol_audio_send(greeting),
                self.user.loop
            ).result()
        self.entry_dumper.start_dump_task("llm", greeting)

        while self.is_stream_audio_data:
            # Use lock to safely check and process transcript
            with self.transcript_lock:
                should_process = self.last_transcript_time and (time.time() - self.last_transcript_time >= 2)
                if should_process:
                    user_response = self.user_input
                    self.user_input = ""
                    self.last_transcript_time = None
                else:
                    user_response = None

            # Process outside the lock to avoid holding it during I/O
            if user_response:
                self.logger.info("Processing final transcript: '%s'", user_response)
                self.request_handler.handle_final_transcript(user_input=user_response)
                with self.transcript_lock:
                    self.last_speak_time = time.time()

            # Check for 30-second timeout with thread safety
            with self.transcript_lock:
                should_repeat = (self.is_completed and self.last_speak_time and
                               (time.time() - self.last_speak_time >= 30))

            if should_repeat:
                if self.last_llm_answer:
                    # Send repeat message using protocol audio
                    if hasattr(self.user, 'stream_id') and self.user.stream_id:
                        import asyncio
                        asyncio.run_coroutine_threadsafe(
                            self.request_handler.text_to_protocol_audio_send(self.last_llm_answer),
                            self.user.loop
                        ).result()
                with self.transcript_lock:
                    self.last_speak_time = time.time()

            time.sleep(0.5)

    def stream_audio_data(self):
        """Stream audio data to OpenAI Whisper using chunked processing"""
        start_time = time.time()

        while self.is_stream_audio_data:
            if time.time() - start_time >= 1800:  # 30-minute timeout
                self.logger.info("30-minute timeout reached. Ending transcription.")

                llm_answer = "Please try again later. Thank you for using Ongo Service"
                if hasattr(self.user, 'stream_id') and self.user.stream_id:
                    import asyncio
                    asyncio.run_coroutine_threadsafe(
                        self.request_handler.text_to_protocol_audio_send(llm_answer),
                        self.user.loop
                    ).result()
                self.user.sync_send(llm_answer)
                self.user.end_ai_call()
                break

            # Process audio chunks from queue
            if self.audio_queue:
                audio_chunk = self.audio_queue.popleft()
                self.audio_buffer.write(audio_chunk)

                # Check if we have enough data for a chunk
                current_size = self.audio_buffer.tell()
                if current_size >= self.chunk_size_bytes:
                    try:
                        # Get audio data for processing
                        self.audio_buffer.seek(0)
                        chunk_data = self.audio_buffer.read(self.chunk_size_bytes)

                        # Add overlap from previous chunk if available
                        if self.last_chunk_overlap:
                            chunk_data = self.last_chunk_overlap + chunk_data

                        # Store overlap for next chunk
                        if len(chunk_data) > self.overlap_size_bytes:
                            self.last_chunk_overlap = chunk_data[-self.overlap_size_bytes:]

                        # Process the chunk
                        result = self._transcribe_audio_chunk(chunk_data)
                        if result:
                            transcript_text = result

                            if transcript_text:
                                # Treat each chunk as a final transcript for now
                                # In a more sophisticated implementation, we could implement
                                # partial transcript logic based on chunk timing
                                self.on_data(transcript_text, is_final=True)

                        # Keep remaining data in buffer
                        remaining_data = self.audio_buffer.read()
                        self.audio_buffer = io.BytesIO()
                        self.audio_buffer.write(remaining_data)

                    except Exception as e:
                        self.logger.error("Error processing audio chunk: %s", e)
                        # Continue processing other chunks
                        continue
            else:
                time.sleep(0.01)

    def start(self):
        """Start the transcription service"""
        pause_thread = threading.Thread(target=self.check_pause_and_handle_transcript, daemon=True)
        pause_thread.start()
        self.stream_audio_data()

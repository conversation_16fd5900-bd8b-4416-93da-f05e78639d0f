from multiprocessing import current_process
import pathlib
import platform
import sys
import os
import socket
from collections import ChainMap
from datetime import datetime
from enum import Enum
from ipaddress import IPv4Address
from typing import Callable, Dict, List, Optional, Union
from uuid import UUID

import psutil
from packaging.version import Version
from pydantic import (BaseModel, DirectoryPath, EmailStr, Field, FilePath,
                      HttpUrl, PositiveFloat, PositiveInt, constr,
                      field_validator)
from pydantic_settings import BaseSettings

from tutor.modules.exceptions import UnsupportedOS


class SSQuality(str, Enum):
    """Quality modes available for speech synthesis.

    >>> SSQuality

    """

    High_Quality = 'high'
    Medium_Quality = 'medium'
    Low_Quality = 'low'

class SupportedPlatforms(str, Enum):
    """Supported operating systems."""

    windows: str = "Windows"
    macOS: str = "Darwin"
    linux: str = "Linux"


supported_platforms = SupportedPlatforms


class Settings(BaseModel):
    """Loads most common system values that do not change.

    >>> Settings

    Raises:
        UnsupportedOS:
        If the hosted device is other than Linux, macOS or Windows.
    """
    if sys.stdin.isatty():
        interactive: bool = True
    else:
        interactive: bool = False
    pid: PositiveInt = os.getpid()
    pname: str = current_process().name
    ram: Union[PositiveInt, PositiveFloat] = psutil.virtual_memory().total
    physical_cores: PositiveInt = psutil.cpu_count(logical=False)
    logical_cores: PositiveInt = psutil.cpu_count(logical=True)
    limited: bool = True if physical_cores < 4 else False
    invoker: str = pathlib.PurePath(sys.argv[0]).stem

    os: str = platform.system()
    if os not in (supported_platforms.macOS, supported_platforms.linux, supported_platforms.windows):
        raise UnsupportedOS(
            f"\n{''.join('*' for _ in range(80))}\n\n"
            "Currently OneCall can run only on Linux, Mac and Windows OS.\n\n"
            f"\n{''.join('*' for _ in range(80))}\n"
        )
    if os == supported_platforms.macOS and Version(platform.mac_ver()[0]) < Version('10.14'):
        legacy: bool = True
    else:
        legacy: bool = False

    is_ongo_faq: bool = True     


settings = Settings()
# Intermittently changes to Windows_NT because of pydantic
if settings.os.startswith('Windows'):
    settings.os = "Windows"


class EnvConfig(BaseSettings):
    model_config = {"env_file": ".env", "env_file_encoding": "utf-8", "extra": "ignore"}
    
    # User add-ons
    birthday: Union[str, None] = None
    title: str = 'sir'
    name: str = 'Customer Support'
    website: HttpUrl = 'https://icharge.com'

    # Communication config
    gmail_user: Union[EmailStr, None] = "<EMAIL>"
    gmail_pass: Union[str, None] = "alzpbuxmndgiihri"
    open_gmail_user: Union[EmailStr, None] = None
    open_gmail_pass: Union[str, None] = None
    recipient: Union[EmailStr, None] = "<EMAIL>"
    phone_number: Union[str, None] = Field(None, pattern="\\d{10}$")

    # Offline communicator config
    offline_host: str = socket.gethostbyname('0.0.0.0')
    offline_port: PositiveInt = Field(default=1801, alias='API_PORT')
    offline_pass: str = 'OfflineComm'
    workers: PositiveInt = 1
    
    # WebSocket server config
    websocket_port: PositiveInt = Field(default=1802, alias='WEBSOCKET_PORT')
    
    # Frontend config
    frontend_port: PositiveInt = Field(default=1800, alias='FRONTEND_PORT')
    backend_proxy_port: PositiveInt = Field(default=1803, alias='BACKEND_PROXY_PORT')

    # Log config
    debug: bool = True  # Mayur change value false to true
    log_retention: Union[int, PositiveInt] = Field(10, lt=90, gt=0)

    # english bot config
    english_bot_token: Union[str, None] = "6378117126:AAExHWVrfsffWG1_DHw408EnNWQCsmPoGSc"
    english_bot_chat_ids: List[int] = [1416702056, 217192255, 217193603]
    english_bot_users: List[str] = ["mayurbt"]
    # english bot Webhook specific
    english_bot_webhook: Union[HttpUrl, None] = None
    english_bot_webhook_ip: Union[IPv4Address, None] = None
    english_bot_endpoint: str = Field("/english-bot-webhook", pattern=r"^\/")
    english_bot_secret: Union[str, None] = Field(None, pattern="^[A-Za-z0-9_-]{1,256}$")
    english_bot_certificate: Union[FilePath, None] = None

    # Speech synthesis config
    speech_synthesis_timeout: int = 3
    speech_synthesis_voice: str = 'en-us_northern_english_male-glow_tts'
    #speech_synthesis_voice: str = 'en-us_southern_english_female-glow_tts'
    speech_synthesis_quality: SSQuality = SSQuality.High_Quality
    speech_synthesis_host: str = socket.gethostbyname('localhost')
    speech_synthesis_port: PositiveInt = 5002
    
    # Transcription service config
    transcription_service: str = Field(default="openai_whisper", pattern="^(assemblyai|aws_transcribe|openai_whisper)$")
    transcription_fallback_enabled: bool = True

    # AssemblyAI config
    assemblyai_api_key: Union[str, None] = "********************************"
    assemblyai_language_code: str = "auto"  # Auto-detection for Hindi and English
    assemblyai_language_options: List[str] = ["hi", "en"]  # Supported languages
    assemblyai_enable_auto_detection: bool = True  # Enable automatic language detection
    assemblyai_speaker_diarization: bool = True  # Enable speaker identification
    assemblyai_confidence_threshold: float = 0.75  # Minimum confidence for language detection

    # Google Cloud Speech config
    google_cloud_project_id: Union[str, None] = "high-verve-469805-r0"
    google_cloud_credentials_path: Union[str, None] = os.path.join(os.path.realpath('tutor'), "fileio", 'high-verve-469805-r0-806945120791.json')
    google_cloud_language_code: str = "en-US"
    google_cloud_sample_rate: int = 16000
    google_cloud_encoding: str = "LINEAR16"

    # AWS Transcribe config
    aws_transcribe_region: str = "us-east-1"
    aws_transcribe_access_key_id: Union[str, None] = "********************"
    aws_transcribe_secret_access_key: Union[str, None] = "dv8pm83uRU4d8xgUbUNW5hDa102kiowKVGQCyGti"
    aws_transcribe_language_code: str = "hi-IN"  # Auto-detection for Hindi and English
    aws_transcribe_language_options: List[str] = ["hi-IN", "en-US"]  # Supported languages
    aws_transcribe_sample_rate: int = 16000
    aws_transcribe_media_encoding: str = "pcm"
    aws_transcribe_vocabulary_name: Union[str, None] = None
    aws_transcribe_vocabulary_filter_name: Union[str, None] = None
    aws_transcribe_enable_partial_results_stabilization: bool = True
    aws_transcribe_partial_results_stability: str = "medium"
    
    # OpenAI TTS config
    openai_api_key: Union[str, None] = "********************************************************"  # Set via environment variable
    openai_tts_model: str = "tts-1"
    openai_tts_voice_english: str = "nova"   # Female voice for English: nova (warm female), shimmer (gentle female)
    openai_tts_voice_hindi: str = "nova"     # Female voice for Hindi/multilingual: nova works well for multilingual
    openai_tts_format: str = "mp3"
    openai_tts_speed: float = 1.0            # Speech speed (0.25 to 4.0)
    openai_tts_timeout: int = 30             # API timeout in seconds

    # OpenAI Whisper config
    openai_whisper_model: str = "whisper-1"                    # Whisper model to use
    openai_whisper_language_code: str = "auto"                 # Language detection: "auto", "hi", "en", etc.
    openai_whisper_language_options: List[str] = ["hi", "en"]  # Supported languages for auto-detection
    openai_whisper_chunk_duration: float = 5.0                # Audio chunk duration in seconds
    openai_whisper_overlap_duration: float = 1.0              # Overlap between chunks in seconds
    openai_whisper_temperature: float = 0.0                   # Sampling temperature (0.0 = deterministic)
    openai_whisper_timeout: int = 30                          # API timeout in seconds
    openai_whisper_max_retries: int = 3                       # Maximum retry attempts for API calls
    openai_whisper_retry_delay: float = 1.0                   # Delay between retries in seconds
    openai_whisper_prompt: Union[str, None] = "This audio contains Hindi and English mixed conversation from India. Please transcribe accurately maintaining both languages."  # Optional prompt for better accuracy
    

env = EnvConfig()


class FileIO(BaseModel):
    """Loads all the files' path required/created by Jarvis.

    >>> FileIO

    """

    # Directories
    root: DirectoryPath = os.path.join(os.path.realpath('tutor'), "fileio")

    # On demand storage
    uploads: DirectoryPath = os.path.join(root, "uploads")
    sessions: DirectoryPath = os.path.join(root, "sessions")
    audio_storage: DirectoryPath = os.path.join(root, "audio_storage")

    # ai tutor internal
    startup_dir: DirectoryPath = os.path.join(root, 'startup')
    location: FilePath = os.path.join(root, 'location.yaml')
    notes: FilePath = os.path.join(root, 'notes.txt')
    processes: FilePath = os.path.join(root, 'processes.yaml')

    # Future useful
    gpt_data: FilePath = os.path.join(root, 'gpt_history.yaml')

    # english_bot
    english_bot_firebase_Certificate: FilePath = os.path.join(root, 'lean-english-4f1ea-firebase-adminsdk-dgd95-1b3aa19c67.json')

    # Speech Synthesis
    speech_synthesis_wav: FilePath = os.path.join(root, 'speech_synthesis.wav')
    # Store log file name in a variable as it is used in multiple modules with file IO
    # todo: remove datetime from id and create log files in dedicated functions
    # todo: check if there are any specific use cases for cid file to have datetime
    speech_synthesis_cid: FilePath = os.path.join(root, 'speech_synthesis.cid')
    speech_synthesis_log: FilePath = datetime.now().strftime(os.path.join('logs', 'speech_synthesis_%d-%m-%Y.log'))

    # Databases
    base_db: FilePath = os.path.join(root, 'database.db')
    task_db: FilePath = os.path.join(root, 'tasks.db')
    stock_db: FilePath = os.path.join(root, 'stock.db')



fileio = FileIO()


class Message:
    def __init__(self, role: str, content: str):
        self.role = role
        self.content = content

    def to_dict(self):
        return {"role": self.role, "content": self.content}

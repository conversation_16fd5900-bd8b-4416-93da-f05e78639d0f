#!/usr/bin/env python3
"""
Test Processing Logic

Direct test of the audio processing conditions.
"""

import os
import sys
import time

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

from tutor.modules.models import models
from tutor.modules.audio.transcription_factory import TranscriptionFactory
from tutor.modules.logger import logger


class MockUser:
    def __init__(self):
        self.mobile = "1234567890"
        self.session = "test_session"
        self.stream_id = None
        self.loop = None
        self.detected_language = None
        
        class MockBotManager:
            def start_new_call(self):
                return "Hello! How can I help you today?"
        
        self.bot_manager = MockBotManager()
    
    def sync_send(self, message):
        pass
    
    def end_ai_call(self):
        pass


def main():
    print("🧪 Testing Processing Logic")
    print("=" * 40)
    
    # Create service
    user = MockUser()
    service = TranscriptionFactory.create_transcription_service(
        user, logger, service_type="openai_whisper"
    )
    
    print(f"Min chunk size: {service.min_chunk_size}")
    print(f"Max wait time: {service.max_wait_time}")
    
    # Add enough chunks to exceed min_chunk_size
    test_chunk = b"x" * 638
    chunks_needed = (service.min_chunk_size // 638) + 2  # Add extra to be sure
    
    print(f"Adding {chunks_needed} chunks...")
    for i in range(chunks_needed):
        service.add_audio_chunk(test_chunk)
        service.audio_buffer.write(test_chunk)
    
    current_size = service.audio_buffer.tell()
    print(f"Buffer size: {current_size}")
    
    # Test condition 1: Size-based
    condition1 = current_size >= service.min_chunk_size
    print(f"Condition 1 (size >= min): {condition1}")
    
    # Test condition 2: Time-based
    service.last_chunk_time = time.time() - service.max_wait_time - 1
    time_since_last = time.time() - service.last_chunk_time
    condition2 = time_since_last >= service.max_wait_time
    print(f"Time since last chunk: {time_since_last:.1f}s")
    print(f"Condition 2 (time >= max): {condition2}")
    
    # Combined condition
    should_process = (condition1 and condition2) or (current_size >= service.chunk_size_bytes)
    print(f"Should process: {should_process}")
    
    if should_process:
        print("✅ Processing conditions met!")
    else:
        print("❌ Processing conditions NOT met")
        print(f"Need: (size >= {service.min_chunk_size} AND time >= {service.max_wait_time}) OR size >= {service.chunk_size_bytes}")
        print(f"Have: size={current_size}, time={time_since_last:.1f}s")


if __name__ == "__main__":
    main()

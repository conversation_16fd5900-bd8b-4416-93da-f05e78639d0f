#!/usr/bin/env python3
"""
Test script for bilingual TTS functionality
Tests both Hindi and English text-to-speech using OpenAI TTS
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tutor.modules.audio.openai_tts import OpenAITTS, OpenAITTSError
from tutor.modules.audio import text_speech


def test_openai_tts_direct():
    """Test OpenAI TTS module directly"""
    print("=== Testing OpenAI TTS Module Directly ===")
    
    # Test English
    try:
        tts = OpenAITTS()
        english_text = "Hello, this is a test in English."
        print(f"Generating English audio for: '{english_text}'")
        
        audio_stream = tts.generate_audio(english_text, 'en')
        print(f"✓ English audio generated successfully: {len(audio_stream.read())} bytes")
        
        # Test Hindi
        hindi_text = "नमस्ते, यह हिंदी में एक टेस्ट है।"
        print(f"Generating Hindi audio for: '{hindi_text}'")
        
        audio_stream_hindi = tts.generate_audio(hindi_text, 'hi')
        print(f"✓ Hindi audio generated successfully: {len(audio_stream_hindi.read())} bytes")
        
        return True
    except OpenAITTSError as e:
        print(f"✗ OpenAI TTS Error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def test_text_speech_integration():
    """Test text_speech module integration"""
    print("\n=== Testing text_speech Module Integration ===")
    
    try:
        # Test English through text_speech module
        english_text = "This is an English test using the text_speech module."
        print(f"Testing English via text_speech: '{english_text}'")
        
        audio_stream_en, audio_file_en = text_speech.tts_instance.generate_audio(english_text, 'en')
        print(f"✓ English integrated test successful: {len(audio_stream_en.read())} bytes")
        
        # Test Hindi through text_speech module
        hindi_text = "यह हिंदी का टेस्ट है text_speech मॉड्यूल के द्वारा।"
        print(f"Testing Hindi via text_speech: '{hindi_text}'")
        
        audio_stream_hi, audio_file_hi = text_speech.tts_instance.generate_audio(hindi_text, 'hi')
        print(f"✓ Hindi integrated test successful: {len(audio_stream_hi.read())} bytes")
        
        return True
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False


async def test_speech_synthesizer():
    """Test async speech_synthesizer function"""
    print("\n=== Testing Async speech_synthesizer Function ===")
    
    try:
        # Test English async
        english_text = "Testing async English speech synthesis."
        print(f"Testing async English synthesis: '{english_text}'")
        
        result_en = await text_speech.speech_synthesizer(english_text, language='en')
        print(f"✓ Async English synthesis result: {result_en}")
        
        # Test Hindi async
        hindi_text = "async हिंदी speech synthesis का टेस्ट।"
        print(f"Testing async Hindi synthesis: '{hindi_text}'")
        
        result_hi = await text_speech.speech_synthesizer(hindi_text, language='hi')
        print(f"✓ Async Hindi synthesis result: {result_hi}")
        
        return True
    except Exception as e:
        print(f"✗ Async synthesis test failed: {e}")
        return False


def check_environment():
    """Check if required environment variables are set"""
    print("=== Checking Environment ===")
    
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        print(f"✓ OPENAI_API_KEY is set (length: {len(openai_key)})")
        return True
    else:
        print("✗ OPENAI_API_KEY is not set")
        print("Please set the OPENAI_API_KEY environment variable")
        return False


async def main():
    """Main test function"""
    print("Bilingual TTS Test Suite")
    print("========================")
    
    # Check environment first
    if not check_environment():
        print("\n❌ Environment check failed. Exiting.")
        return
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Direct OpenAI TTS
    if test_openai_tts_direct():
        tests_passed += 1
    
    # Test 2: text_speech integration
    if test_text_speech_integration():
        tests_passed += 1
    
    # Test 3: Async speech synthesizer
    if await test_speech_synthesizer():
        tests_passed += 1
    
    # Test 4: Error handling (simulate missing API key)
    print("\n=== Testing Error Handling ===")
    try:
        # Temporarily clear the API key to test error handling
        original_key = os.environ.get('OPENAI_API_KEY')
        if 'OPENAI_API_KEY' in os.environ:
            del os.environ['OPENAI_API_KEY']
        
        # This should fail gracefully
        tts_error = OpenAITTS()
        print("✗ Error handling test failed - should have thrown exception")
    except OpenAITTSError as e:
        print(f"✓ Error handling test passed: {e}")
        tests_passed += 1
    except Exception as e:
        print(f"✗ Unexpected error in error handling test: {e}")
    finally:
        # Restore the API key
        if original_key:
            os.environ['OPENAI_API_KEY'] = original_key
    
    # Summary
    print(f"\n=== Test Results ===")
    print(f"Tests passed: {tests_passed}/{total_tests}")
    if tests_passed == total_tests:
        print("🎉 All tests passed! Bilingual TTS is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    # Run the test suite
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
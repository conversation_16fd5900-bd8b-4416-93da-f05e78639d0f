#!/usr/bin/env python3
"""
Test script to simulate AWS Transcribe workflow with language detection
This script simulates the complete workflow without actual audio input
"""

import os
import sys
import time
import asyncio
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tutor.modules.audio.aws_transcribe.realtime_transcription import RealtimeTranscription
from tutor.executors.request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from tutor.executors.entry_dumper import EntryDumper
from tutor.modules.models.classes import env
import logging


class MockUser:
    """Mock user object for testing"""
    def __init__(self):
        self.mobile = "1234567890"
        self.session = "test_session_" + str(int(time.time()))
        self.detected_language = None
        self.stream_id = "mock_stream_123"
        self.loop = asyncio.get_event_loop()
        self.ai_start_listening = True
    
    def sync_send(self, data):
        print(f"[MockUser] Sending: {data}")
    
    def send_end_call(self):
        print("[MockUser] End call signal sent")
    
    async def send_media_event(self, audio_data, mark_name, is_protocol_format=False):
        print(f"[MockUser] Media event sent: mark={mark_name}, audio_size={len(audio_data)}, protocol={is_protocol_format}")


class MockLogger:
    """Mock logger for testing"""
    def info(self, msg, *args):
        print(f"[INFO] {msg % args if args else msg}")
    
    def error(self, msg, *args):
        print(f"[ERROR] {msg % args if args else msg}")
    
    def warning(self, msg, *args):
        print(f"[WARNING] {msg % args if args else msg}")


class MockBotManager:
    """Mock bot manager for testing"""
    def start_new_call(self):
        return "Welcome! How can I help you today? आप कैसे हैं?"
    
    async def handle_user_input(self, user_input):
        # Simulate AI response in the same language as input
        if any(ord(char) >= 0x0900 and ord(char) <= 0x097F for char in user_input):  # Contains Devanagari
            return f"आपने कहा: {user_input}. मैं आपकी मदद कर सकता हूं।"
        else:
            return f"You said: {user_input}. I can help you with that."
    
    def process_llm_answer(self, llm_answer):
        return False, None, llm_answer  # No special processing needed
    
    def handle_no_vehicle_case(self, llm_answer):
        return llm_answer, False


async def test_language_detection():
    """Test language detection functionality"""
    print("=== Testing Language Detection ===")
    
    # Create mock objects
    user = MockUser()
    logger = MockLogger()
    user.bot_manager = MockBotManager()
    
    # Create AWS Transcribe instance
    try:
        transcribe = RealtimeTranscription(
            region=env.aws_transcribe_region,
            access_key_id=env.aws_transcribe_access_key_id,
            secret_access_key=env.aws_transcribe_secret_access_key,
            user=user,
            logger=logger,
            language_code="auto",
            language_options=["hi-IN", "en-US"]
        )
        
        print("✓ AWS Transcribe instance created successfully")
        
        # Test language mapping
        test_cases = [
            ("hi-IN", "hi"),
            ("en-US", "en"),
            ("unknown-code", "en")  # Should default to English
        ]
        
        for aws_code, expected_simple in test_cases:
            result = transcribe._map_language_code(aws_code)
            if result == expected_simple:
                print(f"✓ Language mapping: {aws_code} -> {result}")
            else:
                print(f"✗ Language mapping failed: {aws_code} -> {result} (expected {expected_simple})")
        
        return True
    except Exception as e:
        print(f"✗ Language detection test failed: {e}")
        return False


async def test_bilingual_response_flow():
    """Test the complete bilingual response flow"""
    print("\n=== Testing Bilingual Response Flow ===")
    
    # Create mock objects
    user = MockUser()
    logger = MockLogger()
    user.bot_manager = MockBotManager()
    
    # Create request handler
    entry_dumper = EntryDumper(user.mobile, user.session, "test_file.yaml")
    request_handler = RequestHandler(user, entry_dumper, logger)
    
    # Test cases with different languages
    test_cases = [
        ("Hello, I need help with my vehicle.", "en"),
        ("नमस्ते, मुझे अपनी गाड़ी के लिए मदद चाहिए।", "hi"),
        ("My vehicle number is ABC 1234.", "en"),
        ("मेरी गाड़ी का नंबर है MH 02 AB 1234।", "hi"),
    ]
    
    for user_input, expected_lang in test_cases:
        print(f"\nTesting input: '{user_input}' (expected language: {expected_lang})")
        
        # Simulate detected language
        user.detected_language = expected_lang
        
        try:
            # Simulate the LLM response
            llm_response = await user.bot_manager.handle_user_input(user_input)
            print(f"AI Response: {llm_response}")
            
            # Test TTS generation with detected language
            print(f"Generating TTS for language: {expected_lang}")
            await request_handler.text_to_protocol_audio_send(llm_response)
            
            print(f"✓ Bilingual flow completed for {expected_lang}")
        except Exception as e:
            print(f"✗ Bilingual flow failed for {expected_lang}: {e}")
            return False
    
    return True


async def test_fallback_mechanisms():
    """Test fallback mechanisms"""
    print("\n=== Testing Fallback Mechanisms ===")
    
    # Test 1: Missing OpenAI API key fallback
    print("Testing OpenAI API key fallback...")
    original_key = os.environ.get('OPENAI_API_KEY')
    
    try:
        # Remove API key temporarily
        if 'OPENAI_API_KEY' in os.environ:
            del os.environ['OPENAI_API_KEY']
        
        # This should fall back to gTTS
        from tutor.modules.audio import text_speech
        
        # Force reinitialize TTS instance
        text_speech.TextToSpeech._instance = None
        tts_instance = text_speech.TextToSpeech(voice_type="alloy")
        
        # Test with fallback
        test_text = "This should use gTTS fallback."
        audio_stream, _ = tts_instance.generate_audio(test_text, 'en')
        print(f"✓ Fallback test passed: {len(audio_stream.read())} bytes generated")
        
    except Exception as e:
        print(f"✗ Fallback test failed: {e}")
        return False
    finally:
        # Restore API key
        if original_key:
            os.environ['OPENAI_API_KEY'] = original_key
    
    return True


def check_configuration():
    """Check AWS Transcribe configuration"""
    print("=== Checking Configuration ===")
    
    config_checks = [
        ("AWS Region", env.aws_transcribe_region),
        ("AWS Access Key ID", env.aws_transcribe_access_key_id),
        ("AWS Secret Access Key", "***" if env.aws_transcribe_secret_access_key else None),
        ("Language Code", env.aws_transcribe_language_code),
        ("Language Options", env.aws_transcribe_language_options),
    ]
    
    all_good = True
    for name, value in config_checks:
        if value:
            print(f"✓ {name}: {'***' if 'Secret' in name else value}")
        else:
            print(f"✗ {name}: Not configured")
            all_good = False
    
    return all_good


async def main():
    """Main test function"""
    print("AWS Transcribe Workflow Test Suite")
    print("==================================")
    
    # Check configuration
    if not check_configuration():
        print("\n❌ Configuration incomplete. Please check AWS credentials.")
        return False
    
    # Run tests
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Language detection
    if await test_language_detection():
        tests_passed += 1
    
    # Test 2: Bilingual response flow
    if await test_bilingual_response_flow():
        tests_passed += 1
    
    # Test 3: Fallback mechanisms
    if await test_fallback_mechanisms():
        tests_passed += 1
    
    # Summary
    print(f"\n=== Test Results ===")
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All workflow tests passed!")
        print("\n📋 Next Steps:")
        print("1. Set OPENAI_API_KEY environment variable")
        print("2. Test with real audio input")
        print("3. Verify AWS Transcribe stream connectivity")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
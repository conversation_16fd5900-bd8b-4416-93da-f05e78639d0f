#!/usr/bin/env python3
"""
Test OpenAI Whisper Audio Processing

This test simulates the actual audio processing flow to verify
that the chunking and transcription logic works correctly.
"""

import os
import sys
import time
import threading
import tempfile
import wave

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

from tutor.modules.models import models
from tutor.modules.audio.transcription_factory import TranscriptionFactory
from tutor.modules.logger import logger


class MockUser:
    def __init__(self):
        self.mobile = "1234567890"
        self.session = "test_session"
        self.stream_id = "test_stream"
        self.loop = None
        self.detected_language = None
        
        class MockBotManager:
            def start_new_call(self):
                return "Hello! How can I help you today?"
        
        self.bot_manager = MockBotManager()
    
    def sync_send(self, message):
        print(f"Mock sync_send: {message}")
    
    def end_ai_call(self):
        print("Mock end_ai_call called")


def create_test_audio_data(duration=2.0, sample_rate=16000):
    """Create test audio data (sine wave)"""
    import numpy as np
    
    # Generate sine wave
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    frequency = 440  # A4 note
    audio_data = np.sin(2 * np.pi * frequency * t)
    
    # Convert to 16-bit PCM
    audio_data = (audio_data * 32767).astype(np.int16)
    return audio_data.tobytes()


def simulate_audio_chunks(service, audio_data, chunk_size=638):
    """Simulate adding audio chunks like the real system"""
    print(f"Simulating audio chunks: {len(audio_data)} bytes total, {chunk_size} bytes per chunk")
    
    chunks_sent = 0
    for i in range(0, len(audio_data), chunk_size):
        chunk = audio_data[i:i + chunk_size]
        service.add_audio_chunk(chunk)
        chunks_sent += 1
        print(f"Sent chunk {chunks_sent}: {len(chunk)} bytes")
        time.sleep(0.02)  # Simulate real-time audio streaming
    
    print(f"Total chunks sent: {chunks_sent}")


def test_audio_processing():
    """Test the audio processing with simulated data"""
    print("🧪 Testing OpenAI Whisper Audio Processing")
    print("=" * 50)
    
    try:
        # Create service
        user = MockUser()
        service = TranscriptionFactory.create_transcription_service(
            user, logger, service_type="openai_whisper"
        )
        
        print(f"✅ Service created")
        print(f"   Chunk duration: {service.chunk_duration}s")
        print(f"   Min chunk size: {service.min_chunk_size} bytes")
        print(f"   Max wait time: {service.max_wait_time}s")
        
        # Connect service
        service.connect()
        print("✅ Service connected")
        
        # Create test audio data
        print("\n🎵 Creating test audio data...")
        audio_data = create_test_audio_data(duration=3.0)  # 3 seconds of audio
        print(f"Created {len(audio_data)} bytes of test audio")
        
        # Start service in background
        print("\n🚀 Starting transcription service...")
        service_thread = threading.Thread(target=service.start, daemon=True)
        service_thread.start()
        
        # Give service time to initialize
        time.sleep(1)
        
        # Simulate audio chunks
        print("\n📡 Simulating audio stream...")
        simulate_audio_chunks(service, audio_data, chunk_size=638)
        
        # Wait for processing
        print("\n⏳ Waiting for transcription results...")
        time.sleep(10)  # Wait for processing
        
        # Close service
        print("\n🔒 Closing service...")
        service.close()
        
        print("\n✅ Audio processing test completed!")
        print("Check the logs above for transcription results and processing details.")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Enable debug logging to see what's happening
    import logging
    logging.getLogger().setLevel(logging.DEBUG)
    
    success = test_audio_processing()
    sys.exit(0 if success else 1)

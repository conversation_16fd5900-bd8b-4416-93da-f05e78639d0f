#!/usr/bin/env python3
"""
Basic OpenAI Whisper Test

Simple test to verify OpenAI Whisper integration is working.
"""

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

from tutor.modules.models import models
from tutor.modules.audio.transcription_factory import TranscriptionFactory
from tutor.modules.logger import logger


class MockUser:
    def __init__(self):
        self.mobile = "1234567890"
        self.session = "test_session"
        self.stream_id = "test_stream"
        self.loop = None
        self.detected_language = None
        
        # Mock bot_manager
        class MockBotManager:
            def start_new_call(self):
                return "Hello! How can I help you today?"
        
        self.bot_manager = MockBotManager()
    
    def sync_send(self, message):
        print(f"Mock sync_send: {message}")
    
    def end_ai_call(self):
        print("Mock end_ai_call called")


def main():
    print("🧪 Basic OpenAI Whisper Test")
    print("=" * 40)
    
    # Check configuration
    print(f"OpenAI API Key: {'Set' if models.env.openai_api_key else 'Not Set'}")
    print(f"Default Service: {models.env.transcription_service}")
    print(f"Whisper Model: {models.env.openai_whisper_model}")
    print(f"Language Code: {models.env.openai_whisper_language_code}")
    print(f"Chunk Duration: {models.env.openai_whisper_chunk_duration}s")
    
    # Check service availability
    available_services = TranscriptionFactory.get_available_services()
    print(f"Available Services: {available_services}")
    
    if "openai_whisper" not in available_services:
        print("❌ OpenAI Whisper not available")
        return False
    
    # Test service creation
    try:
        user = MockUser()
        print("\n🔧 Creating OpenAI Whisper service...")
        
        service = TranscriptionFactory.create_transcription_service(
            user, logger, service_type="openai_whisper"
        )
        
        print(f"✅ Service created: {type(service).__name__}")
        print(f"   Model: {service.model}")
        print(f"   Language: {service.language_code}")
        print(f"   Chunk Duration: {service.chunk_duration}s")
        print(f"   Temperature: {service.temperature}")
        
        # Test connection
        print("\n🔗 Testing connection...")
        service.connect()
        print("✅ Connection successful")
        
        # Test close
        print("\n🔒 Testing close...")
        service.close()
        print("✅ Close successful")
        
        print("\n🎉 Basic test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
OpenAI Whisper Integration Test Suite

This test suite validates the OpenAI Whisper real-time transcription implementation
including configuration, service creation, error handling, and integration with
the existing transcription factory.

Usage:
    python test_openai_whisper_integration.py

Requirements:
    - OpenAI API key configured in environment
    - All required dependencies installed
    - Test audio files (optional)

Author: AI Assistant
Date: 2025-08-23
"""

import os
import sys
import time
import threading
import tempfile
import wave
import io
from unittest.mock import Mock, patch

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

from tutor.modules.models import models
from tutor.modules.audio.transcription_factory import TranscriptionFactory, TranscriptionServiceError
from tutor.modules.logger import logger


class MockUser:
    """Mock user class for testing"""
    def __init__(self):
        self.mobile = "1234567890"
        self.session = "test_session_123"
        self.stream_id = "test_stream_123"
        self.loop = None
        self.detected_language = None
        self.bot_manager = Mock()
        self.bot_manager.start_new_call.return_value = "Hello! How can I help you today?"
    
    def sync_send(self, message):
        print(f"Mock sync_send: {message}")
    
    def end_ai_call(self):
        print("Mock end_ai_call called")


def test_configuration():
    """Test OpenAI Whisper configuration"""
    print("=" * 60)
    print("Testing OpenAI Whisper Configuration")
    print("=" * 60)
    
    # Test configuration values
    config_tests = [
        ("openai_api_key", models.env.openai_api_key),
        ("openai_whisper_model", models.env.openai_whisper_model),
        ("openai_whisper_language_code", models.env.openai_whisper_language_code),
        ("openai_whisper_language_options", models.env.openai_whisper_language_options),
        ("openai_whisper_chunk_duration", models.env.openai_whisper_chunk_duration),
        ("openai_whisper_overlap_duration", models.env.openai_whisper_overlap_duration),
        ("openai_whisper_temperature", models.env.openai_whisper_temperature),
        ("openai_whisper_timeout", models.env.openai_whisper_timeout),
        ("openai_whisper_max_retries", models.env.openai_whisper_max_retries),
        ("openai_whisper_retry_delay", models.env.openai_whisper_retry_delay),
        ("openai_whisper_prompt", models.env.openai_whisper_prompt),
    ]
    
    for config_name, config_value in config_tests:
        print(f"✓ {config_name}: {config_value}")
    
    # Test transcription service default
    print(f"✓ Default transcription service: {models.env.transcription_service}")
    print(f"✓ Fallback enabled: {models.env.transcription_fallback_enabled}")
    
    print("\n✅ Configuration test completed successfully!")
    return True


def test_service_availability():
    """Test OpenAI Whisper service availability"""
    print("=" * 60)
    print("Testing Service Availability")
    print("=" * 60)
    
    # Test available services
    available_services = TranscriptionFactory.get_available_services()
    print(f"Available services: {available_services}")
    
    if "openai_whisper" in available_services:
        print("✅ OpenAI Whisper service is available")
    else:
        print("❌ OpenAI Whisper service is NOT available")
        return False
    
    # Test service validation
    is_valid, error_msg = TranscriptionFactory.validate_service_config("openai_whisper")
    if is_valid:
        print("✅ OpenAI Whisper configuration is valid")
    else:
        print(f"❌ OpenAI Whisper configuration is invalid: {error_msg}")
        return False
    
    # Test service health
    is_healthy, status_msg = TranscriptionFactory.check_service_health("openai_whisper")
    if is_healthy:
        print(f"✅ OpenAI Whisper service is healthy: {status_msg}")
    else:
        print(f"❌ OpenAI Whisper service is unhealthy: {status_msg}")
        return False
    
    print("\n✅ Service availability test completed successfully!")
    return True


def test_service_creation():
    """Test OpenAI Whisper service creation"""
    print("=" * 60)
    print("Testing Service Creation")
    print("=" * 60)
    
    try:
        # Create mock user
        user = MockUser()
        
        # Test service creation
        print("Creating OpenAI Whisper transcription service...")
        transcription_service = TranscriptionFactory.create_transcription_service(
            user, logger, service_type="openai_whisper"
        )
        
        print(f"✅ Service created successfully: {type(transcription_service).__name__}")
        
        # Test service attributes
        required_attributes = [
            'api_key', 'model', 'language_code', 'chunk_duration', 
            'overlap_duration', 'temperature', 'timeout', 'client'
        ]
        
        for attr in required_attributes:
            if hasattr(transcription_service, attr):
                value = getattr(transcription_service, attr)
                print(f"✓ {attr}: {value}")
            else:
                print(f"❌ Missing attribute: {attr}")
                return False
        
        # Test service methods
        required_methods = [
            'connect', 'close', 'add_audio_chunk', 'start',
            'on_open', 'on_data', 'on_error', 'on_close'
        ]
        
        for method in required_methods:
            if hasattr(transcription_service, method):
                print(f"✓ Method available: {method}")
            else:
                print(f"❌ Missing method: {method}")
                return False
        
        print("\n✅ Service creation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Service creation failed: {e}")
        return False


def test_fallback_mechanism():
    """Test fallback mechanism"""
    print("=" * 60)
    print("Testing Fallback Mechanism")
    print("=" * 60)
    
    # Test fallback service selection
    fallback_tests = [
        ("openai_whisper", "assemblyai"),
        ("assemblyai", "openai_whisper"),
        ("aws_transcribe", "openai_whisper"),
    ]
    
    for primary, expected_fallback in fallback_tests:
        fallback = TranscriptionFactory.get_fallback_service(primary)
        if fallback == expected_fallback:
            print(f"✅ {primary} -> {fallback}")
        else:
            print(f"❌ {primary} -> {fallback} (expected {expected_fallback})")
    
    print("\n✅ Fallback mechanism test completed successfully!")
    return True


def create_test_audio_file(duration=2.0, sample_rate=16000):
    """Create a test audio file with sine wave"""
    import numpy as np
    
    # Generate sine wave
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    frequency = 440  # A4 note
    audio_data = np.sin(2 * np.pi * frequency * t)
    
    # Convert to 16-bit PCM
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # Create temporary WAV file
    temp_fd, temp_path = tempfile.mkstemp(suffix='.wav')
    
    with os.fdopen(temp_fd, 'wb') as temp_file:
        with wave.open(temp_file, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
    
    return temp_path


def test_audio_processing():
    """Test audio processing functionality"""
    print("=" * 60)
    print("Testing Audio Processing")
    print("=" * 60)
    
    try:
        # Create mock user
        user = MockUser()
        
        # Create service
        transcription_service = TranscriptionFactory.create_transcription_service(
            user, logger, service_type="openai_whisper"
        )
        
        # Test audio file creation
        print("Creating test audio file...")
        test_audio_path = create_test_audio_file(duration=1.0)
        
        # Test audio file processing
        print("Testing audio file creation method...")
        with open(test_audio_path, 'rb') as f:
            audio_data = f.read()
        
        # Test the _create_audio_file method
        temp_path = transcription_service._create_audio_file(audio_data[44:])  # Skip WAV header
        
        if os.path.exists(temp_path):
            print(f"✅ Audio file created successfully: {temp_path}")
            os.unlink(temp_path)  # Clean up
        else:
            print("❌ Audio file creation failed")
            return False
        
        # Clean up test file
        os.unlink(test_audio_path)
        
        print("\n✅ Audio processing test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Audio processing test failed: {e}")
        return False


def test_error_handling():
    """Test error handling"""
    print("=" * 60)
    print("Testing Error Handling")
    print("=" * 60)
    
    try:
        # Test with invalid API key
        print("Testing invalid API key handling...")
        
        # Temporarily modify the API key
        original_key = models.env.openai_api_key
        models.env.openai_api_key = "invalid_key_test"
        
        try:
            user = MockUser()
            transcription_service = TranscriptionFactory.create_transcription_service(
                user, logger, service_type="openai_whisper"
            )
            print("✅ Service created with invalid key (will fail on actual API call)")
        except TranscriptionServiceError as e:
            print(f"✅ Properly caught invalid API key error: {e}")
        finally:
            # Restore original key
            models.env.openai_api_key = original_key
        
        # Test with missing API key
        print("Testing missing API key handling...")
        models.env.openai_api_key = None
        
        try:
            user = MockUser()
            transcription_service = TranscriptionFactory.create_transcription_service(
                user, logger, service_type="openai_whisper"
            )
            print("❌ Service should not have been created with missing API key")
            return False
        except TranscriptionServiceError as e:
            print(f"✅ Properly caught missing API key error: {e}")
        finally:
            # Restore original key
            models.env.openai_api_key = original_key
        
        print("\n✅ Error handling test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        # Restore original key
        models.env.openai_api_key = original_key
        return False


def run_all_tests():
    """Run all tests"""
    print("🚀 Starting OpenAI Whisper Integration Test Suite")
    print("=" * 60)
    
    tests = [
        ("Configuration", test_configuration),
        ("Service Availability", test_service_availability),
        ("Service Creation", test_service_creation),
        ("Fallback Mechanism", test_fallback_mechanism),
        ("Audio Processing", test_audio_processing),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! OpenAI Whisper integration is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

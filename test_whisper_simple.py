#!/usr/bin/env python3
"""
Simple OpenAI Whisper Test

Test just the audio processing logic without complex threading.
"""

import os
import sys
import time

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

from tutor.modules.models import models
from tutor.modules.audio.transcription_factory import TranscriptionFactory
from tutor.modules.logger import logger


class MockUser:
    def __init__(self):
        self.mobile = "1234567890"
        self.session = "test_session"
        self.stream_id = None
        self.loop = None
        self.detected_language = None
        
        class MockBotManager:
            def start_new_call(self):
                return "Hello! How can I help you today?"
        
        self.bot_manager = MockBotManager()
    
    def sync_send(self, message):
        print(f"Mock sync_send: {message}")
    
    def end_ai_call(self):
        print("Mock end_ai_call called")


def test_simple_processing():
    """Test simple audio processing"""
    print("🧪 Simple OpenAI Whisper Test")
    print("=" * 40)
    
    try:
        # Create service
        user = MockUser()
        service = TranscriptionFactory.create_transcription_service(
            user, logger, service_type="openai_whisper"
        )
        
        print(f"✅ Service created")
        print(f"   is_stream_audio_data: {service.is_stream_audio_data}")
        print(f"   audio_queue length: {len(service.audio_queue)}")
        
        # Connect service
        service.connect()
        print("✅ Service connected")
        
        # Add some test audio chunks
        print("\n📡 Adding test audio chunks...")
        test_chunk = b"x" * 638  # Simulate 638-byte chunks
        
        for i in range(20):
            service.add_audio_chunk(test_chunk)
            print(f"Added chunk {i+1}, queue size: {len(service.audio_queue)}")
        
        print(f"\n📊 Final queue size: {len(service.audio_queue)}")
        print(f"Buffer size: {service.audio_buffer.tell()}")
        
        # Manually call the audio processing logic
        print("\n🔧 Testing audio processing logic...")

        # Process enough chunks to reach min_chunk_size
        chunks_needed = (service.min_chunk_size // 638) + 1
        print(f"Need {chunks_needed} chunks to reach min_chunk_size of {service.min_chunk_size}")

        for i in range(chunks_needed):
            if service.audio_queue:
                audio_chunk = service.audio_queue.popleft()
                service.audio_buffer.write(audio_chunk)
                print(f"Processed chunk {i+1}, buffer size now: {service.audio_buffer.tell()}")

        # Check processing conditions
        current_time = time.time()
        current_size = service.audio_buffer.tell()
        time_since_last_chunk = current_time - service.last_chunk_time

        should_process = (
            (current_size >= service.min_chunk_size and time_since_last_chunk >= service.max_wait_time) or
            (current_size >= service.chunk_size_bytes)
        )

        print(f"\n📊 Processing conditions:")
        print(f"Current size: {current_size}")
        print(f"Min chunk size: {service.min_chunk_size}")
        print(f"Time since last chunk: {time_since_last_chunk:.1f}s")
        print(f"Max wait time: {service.max_wait_time}s")
        print(f"Should process: {should_process}")

        # Test with time condition
        print(f"\n⏰ Testing time-based processing...")
        service.last_chunk_time = current_time - service.max_wait_time - 1  # Simulate old chunk
        time_since_last_chunk = current_time - service.last_chunk_time

        should_process_time = (
            (current_size >= service.min_chunk_size and time_since_last_chunk >= service.max_wait_time) or
            (current_size >= service.chunk_size_bytes)
        )

        print(f"Time since last chunk (simulated): {time_since_last_chunk:.1f}s")
        print(f"Should process (with time): {should_process_time}")
        
        # Close service
        service.close()
        print("\n✅ Simple test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_simple_processing()
    sys.exit(0 if success else 1)

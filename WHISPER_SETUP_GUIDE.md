# OpenAI Whisper Setup Guide

## Quick Start

### 1. Prerequisites
- OpenAI API key
- Python environment with required dependencies

### 2. Installation
```bash
# Install OpenAI library if not already installed
pip install openai
```

### 3. Configuration
Set your OpenAI API key:
```bash
export OPENAI_API_KEY="your_openai_api_key_here"
```

### 4. Test Installation
```bash
cd one_call
python test_whisper_basic.py
```

Expected output:
```
🧪 Basic OpenAI Whisper Test
========================================
OpenAI API Key: Set
Default Service: openai_whisper
...
🎉 Basic test completed successfully!
```

## Configuration Options

### Default Settings (Production Ready)
```python
# Already configured in tutor/modules/models/classes.py
openai_whisper_model = "whisper-1"
openai_whisper_language_code = "auto"
openai_whisper_chunk_duration = 5.0
openai_whisper_overlap_duration = 1.0
openai_whisper_temperature = 0.0
```

### Custom Configuration
To modify settings, edit `tutor/modules/models/classes.py`:

```python
# For faster response (less accuracy)
openai_whisper_chunk_duration: float = 3.0
openai_whisper_overlap_duration: float = 0.5

# For higher accuracy (slower response)
openai_whisper_chunk_duration: float = 8.0
openai_whisper_overlap_duration: float = 2.0

# For specific language (no auto-detection)
openai_whisper_language_code: str = "hi"  # Hindi only
openai_whisper_language_code: str = "en"  # English only
```

## Usage Examples

### Basic Usage
The service is automatically used when `transcription_service = "openai_whisper"` (default).

### Manual Service Selection
```python
from tutor.modules.audio.transcription_factory import TranscriptionFactory

# Force OpenAI Whisper
service = TranscriptionFactory.create_transcription_service(
    user, logger, service_type="openai_whisper"
)
```

### With Fallback Disabled
```python
service = TranscriptionFactory.create_transcription_service(
    user, logger, 
    service_type="openai_whisper",
    enable_fallback=False
)
```

## Verification

### Check Service Status
```python
from tutor.modules.audio.transcription_factory import TranscriptionFactory

# Check if service is available
available = TranscriptionFactory.get_available_services()
print("OpenAI Whisper available:", "openai_whisper" in available)

# Check service health
healthy, msg = TranscriptionFactory.check_service_health("openai_whisper")
print(f"Service health: {healthy} - {msg}")
```

### Run Comprehensive Tests
```bash
python test_openai_whisper_integration.py
```

## Troubleshooting

### Common Issues

1. **"OpenAI API key not configured"**
   - Set the OPENAI_API_KEY environment variable
   - Verify the key is valid and has credits

2. **"OpenAI library not installed"**
   ```bash
   pip install openai
   ```

3. **Service not available**
   - Check API key configuration
   - Verify network connectivity
   - Check OpenAI service status

### Debug Information
```python
from tutor.modules.models import models

print(f"API Key: {'Set' if models.env.openai_api_key else 'Not Set'}")
print(f"Model: {models.env.openai_whisper_model}")
print(f"Language: {models.env.openai_whisper_language_code}")
```

## Performance Tuning

### For Low Latency
```python
openai_whisper_chunk_duration = 3.0
openai_whisper_overlap_duration = 0.5
openai_whisper_timeout = 15
```

### For High Accuracy
```python
openai_whisper_chunk_duration = 8.0
openai_whisper_overlap_duration = 2.0
openai_whisper_temperature = 0.0
```

### For Cost Optimization
```python
openai_whisper_chunk_duration = 6.0  # Larger chunks = fewer API calls
openai_whisper_max_retries = 2       # Fewer retries
```

## Production Checklist

- [ ] OpenAI API key configured
- [ ] Service health check passes
- [ ] Basic test passes
- [ ] Comprehensive test suite passes
- [ ] Fallback services configured
- [ ] Monitoring and logging enabled
- [ ] Performance settings optimized
- [ ] Error handling tested

## Next Steps

1. **Integration**: The service is now ready for production use
2. **Monitoring**: Set up monitoring for API usage and errors
3. **Optimization**: Tune parameters based on your specific use case
4. **Scaling**: Monitor performance under load

## Support

If you encounter issues:
1. Run the basic test first
2. Check the comprehensive test results
3. Verify your OpenAI API key and credits
4. Review the error logs for specific issues
